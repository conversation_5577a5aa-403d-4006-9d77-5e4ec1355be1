import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart';
import 'package:moneymouthy/features/wallet/presentation/components/transaction_tile.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_states.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:toastification/toastification.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_balance_cubit.dart';

class WalletPage extends StatefulWidget {
  const WalletPage({super.key});

  @override
  State<WalletPage> createState() => _WalletPageState();
}

class _WalletPageState extends State<WalletPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _amountController = TextEditingController();

  // cubits
  late final walletCubit = context.read<WalletCubit>();
  late final authCubit = context.read<AuthCubit>();
  late final balanceCubit = context.read<WalletBalanceCubit>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadWalletData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  void _loadWalletData() {
    final user = authCubit.currentUser;
    if (user != null) {
      walletCubit.loadWallet(user.uid);
    }
  }

  // process payment
  void processPayment() {
    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Validation Error'),
        description: Text('Please enter an amount'),
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Validation Error'),
        description: Text('Please enter valid amount'),
        type: ToastificationType.info,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
      return;
    }

    if (amount < 2.0) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Validation Error'),
        description: Text('Minimum Amount is \$2.0'),
        type: ToastificationType.info,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
      return;
    }
    // Process Payment
    if (kIsWeb) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Allow Popups on Web'),
        description: Text(
          'Kindly allow popups on web to complete payment, if they are blocked.',
        ),
        type: ToastificationType.warning,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
    }
    final user = authCubit.currentUser;
    if (user != null) {
      walletCubit.addBalance(user.uid, amount, 'Wallet Funding via Stripe');
    }
    _amountController.clear();
    Navigator.pop(context);
  }

  void _showAddBalanceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Balance'),

        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Preset amounts
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                for (int i = 1; i <= 5; i++)
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(100, 40),
                      backgroundColor: Theme.of(context).colorScheme.tertiary,
                      foregroundColor: Theme.of(context).colorScheme.surface,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: () {
                      _amountController.text = (i * 10).toString();
                    },
                    child: Text('\$${i * 10}'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Amount',
                prefixText: '\$',
                border: OutlineInputBorder(),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  RegExp(r'^\d+\.?\d{0,2}'),
                ), // allow only
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.tertiary,
              foregroundColor: Theme.of(context).colorScheme.surface,
            ),
            onPressed: processPayment,
            child: Text(
              'Add',
              style: TextStyle(color: Theme.of(context).colorScheme.surface),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: ConstrainedScaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          centerTitle: true,
          title: Text('Wallet'),
          bottom: TabBar(
            controller: _tabController,
            dividerColor: Colors.transparent,
            labelColor: Theme.of(context).colorScheme.inversePrimary,
            unselectedLabelColor: Theme.of(context).colorScheme.primary,
            tabs: const [
              Tab(text: 'Deposits'),
              Tab(text: 'All'),
              Tab(text: 'Spent'),
            ],
          ),
        ),
        body: BlocBuilder<WalletCubit, WalletStates>(
          builder: (context, state) {
            if (state is WalletLoading) {
              return const Center(child: LoadingIndicator());
            }

            if (state is WalletError) {
              // sleep for 3 seconds and then retry
              Future.delayed(const Duration(seconds: 3), () {
                walletCubit.loadWallet(authCubit.currentUser!.uid);
              });
              return Center(
                child: Column(
                  children: [
                    Text(
                      state.message,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ),
              );
            }

            if (state is WalletLoaded) {
              return Column(
                children: [
                  // Balance Card
                  Container(
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.surfaceTint,
                          Theme.of(context).colorScheme.tertiary,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Current Balance',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.white.withAlpha(200)),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '\$${state.wallet.balance.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.headlineLarge
                              ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _showAddBalanceDialog,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: Theme.of(
                                context,
                              ).colorScheme.primary,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              '+ Add Balance',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.tertiary,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Transactions List
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildTransactionsList(
                          state.transactions
                              .where((t) => t.type == TransactionType.deposit)
                              .toList(),
                        ),
                        _buildTransactionsList(state.transactions),
                        _buildTransactionsList(
                          state.transactions
                              .where((t) => t.type == TransactionType.spent)
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }

            return const Center(child: Text('Something went wrong'));
          },
        ),
      ),
    );
  }

  Widget _buildTransactionsList(List<Transaction> transactions) {
    if (transactions.isEmpty) {
      return const Center(child: Text('No transactions yet'));
    }

    return ListView.builder(
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        return TransactionTile(transaction: transactions[index]);
      },
    );
  }
}
