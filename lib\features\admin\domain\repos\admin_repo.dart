/*

Admin Repository

- check if user is admin
- logout

*/

import 'package:moneymouthy/features/admin/domain/entities/admin_user.dart';
import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';

abstract class AdminRepo {
  Future<bool> checkAdmin(String uid);
  Future<void> logout();
  // get admin user
  Future<AppUser> getAdminUser(String uid);

  // get current user
  Future<AdminUser> getCurrentUser();
}
