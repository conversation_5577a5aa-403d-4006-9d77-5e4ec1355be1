import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:moneymouthy/features/storage/domain/storage_repo.dart';

class FirebaseStorageRepo implements StorageRepo {
  final FirebaseStorage firebaseStorage = FirebaseStorage.instance;
  final String profileFolder = 'profile_images_new';
  final String postFolder = 'post_images_new';
  final String videoFolder = 'post_videos_new';

  /*
  PROFILE PICTURES - upload profile pictures to storage
  */
  @override
  Future<String?> uploadProfileImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, profileFolder);
  }

  @override
  Future<String?> uploadProfileImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, profileFolder);
  }

  /*
  POST PICTURES - upload post pictures to storage
  */
  @override
  Future<String?> uploadPostImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, postFolder);
  }

  @override
  Future<String?> uploadPostImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, postFolder);
  }

  /// HELPER METHODS - to upload files to storage

  Future<String?> _uploadFile(
    String path,
    String fileName,
    String folder,
  ) async {
    try {
      final file = File(path);
      if (!await file.exists()) {
        throw Exception('File does not exist: $path');
      }
      if (await file.length() == 0) {
        throw Exception('File is empty: $path');
      }

      final storageRef = firebaseStorage.ref().child("$folder/$fileName");
      final metadata = SettableMetadata(contentType: _getContentType(fileName));

      final uploadTask = await storageRef.putFile(file, metadata);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Upload failed for $fileName: $e');
      return null;
    }
  }

  Future<String?> _uploadFileBytes(
    Uint8List fileBytes,
    String fileName,
    String folder,
  ) async {
    try {
      if (fileBytes.isEmpty) {
        throw Exception('File bytes are empty');
      }

      final storageRef = firebaseStorage.ref().child("$folder/$fileName");
      final metadata = SettableMetadata(contentType: _getContentType(fileName));

      final uploadTask = await storageRef.putData(fileBytes, metadata);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Upload failed for $fileName: $e');
      return null;
    }
  }

  @override
  Future<String?> uploadPostVideoMobile(String path, String fileName) async {
    return await _uploadVideoFile(path, videoFolder, fileName);
  }

  @override
  Future<String?> uploadPostVideoWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return await _uploadVideoBytes(fileBytes, videoFolder, fileName);
  }

  @override
  Future<List<String>> uploadMultiplePostImagesMobile(
    List<String> paths,
    String postId,
  ) async {
    if (paths.isEmpty) return [];

    try {
      const int batchSize = 3;
      final List<String> successfulUploads = [];

      for (int i = 0; i < paths.length; i += batchSize) {
        final end = (i + batchSize < paths.length)
            ? i + batchSize
            : paths.length;
        final batch = paths.sublist(i, end);

        final batchTasks = batch.asMap().entries.map((entry) {
          final index = i + entry.key;
          final fileName = '${postId}_image_$index.jpg';
          return _uploadFile(entry.value, fileName, postFolder);
        }).toList();

        final batchResults = await Future.wait(batchTasks);
        successfulUploads.addAll(batchResults.whereType<String>());
      }

      if (successfulUploads.isEmpty && paths.isNotEmpty) {
        throw Exception('All image uploads failed');
      }

      return successfulUploads;
    } catch (e) {
      debugPrint('Error in batch upload: $e');
      rethrow;
    }
  }

  @override
  Future<List<String>> uploadMultiplePostImagesWeb(
    List<Uint8List> fileBytes,
    String postId,
  ) async {
    if (fileBytes.isEmpty) return [];

    const int batchSize = 3;
    final List<String> successfulUploads = [];

    for (int i = 0; i < fileBytes.length; i += batchSize) {
      final end = (i + batchSize < fileBytes.length)
          ? i + batchSize
          : fileBytes.length;
      final batch = fileBytes.sublist(i, end);

      final batchTasks = batch.asMap().entries.map((entry) {
        final index = i + entry.key;
        final fileName = '${postId}_image_$index.jpg';
        return _uploadFileBytes(entry.value, fileName, postFolder);
      }).toList();

      final batchResults = await Future.wait(batchTasks);
      successfulUploads.addAll(batchResults.whereType<String>());
    }

    if (successfulUploads.isEmpty && fileBytes.isNotEmpty) {
      throw Exception('All image uploads failed');
    }

    return successfulUploads;
  }

  Future<String?> _uploadVideoFile(
    String path,
    String folder,
    String fileName,
  ) async {
    try {
      final file = File(path);
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(contentType: 'video/mp4');

      final uploadTask = await storageRef.putFile(file, metadata);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Video upload failed: $e');
      return null;
    }
  }

  Future<String?> _uploadVideoBytes(
    Uint8List fileBytes,
    String folder,
    String fileName,
  ) async {
    try {
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(contentType: 'video/mp4');

      final uploadTask = await storageRef.putData(fileBytes, metadata);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Video upload failed: $e');
      return null;
    }
  }

  String _getContentType(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'mp4':
        return 'video/mp4';
      default:
        return 'application/octet-stream';
    }
  }
}
