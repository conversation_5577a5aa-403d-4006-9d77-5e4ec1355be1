import 'dart:io';

import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:moneymouthy/features/storage/domain/storage_repo.dart';
import 'package:moneymouthy/features/storage/services/image_optimization_service.dart';

class FirebaseStorageRepo implements StorageRepo {
  final FirebaseStorage firebaseStorage = FirebaseStorage.instance;
  final String profileFolder = 'profile_images_new';
  final String postFolder = 'post_images_new';
  final String videoFolder = 'post_videos_new';

  // Cache for optimized images to avoid re-optimization
  static final Map<String, dynamic> _optimizedCache = {};

  /*
  PROFILE PICTURES - upload profile pictures to storage
  */
  // mobile platforms
  @override
  Future<String?> uploadProfileImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, profileFolder);
  }

  // web platforms
  @override
  Future<String?> uploadProfileImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, profileFolder);
  }

  /*
  POST PICTURES - upload post pictures to storage
  */
  // mobile platforms
  @override
  Future<String?> uploadPostImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, postFolder);
  }

  // web platforms
  @override
  Future<String?> uploadPostImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, postFolder);
  }

  /// HELPER METHODS - to upload files to storage

  // mobile platform (file) - Simplified without optimization during upload
  Future<String?> _uploadFile(
    String path,
    String fileName,
    String folder,
  ) async {
    const int maxRetries = 2; // Reduced retries for faster failure
    const Duration retryDelay = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check cache first
        final cacheKey = '$path-$fileName';
        if (_optimizedCache.containsKey(cacheKey)) {
          final cachedPath = _optimizedCache[cacheKey] as String;
          final cachedFile = File(cachedPath);
          if (await cachedFile.exists()) {
            path = cachedPath;
            debugPrint('Using cached optimized image');
          }
        }

        final file = File(path);
        if (!await file.exists()) {
          throw Exception('File does not exist: $path');
        }

        // Skip optimization during upload - should be done beforehand
        final storageRef = firebaseStorage.ref().child("$folder/$fileName");

        final metadata = SettableMetadata(
          contentType: _getContentType(fileName),
          cacheControl: 'public, max-age=3600', // Add cache control
        );

        final uploadTask = await storageRef.putFile(file, metadata);
        final downloadUrl = await uploadTask.ref.getDownloadURL();

        debugPrint('Successfully uploaded $fileName on attempt $attempt');
        return downloadUrl;
      } catch (e) {
        debugPrint('Upload attempt $attempt failed for $fileName: $e');

        if (attempt == maxRetries) {
          return null;
        }

        await Future.delayed(retryDelay * attempt);
      }
    }
    return null;
  }

  // web platform (bytes) - Simplified without optimization during upload
  Future<String?> _uploadFileBytes(
    Uint8List fileBytes,
    String fileName,
    String folder,
  ) async {
    const int maxRetries = 2;
    const Duration retryDelay = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (fileBytes.isEmpty) {
          throw Exception('File bytes are empty');
        }

        final storageRef = firebaseStorage.ref().child("$folder/$fileName");

        final metadata = SettableMetadata(
          contentType: _getContentType(fileName),
          cacheControl: 'public, max-age=3600',
        );

        final uploadTask = await storageRef.putData(fileBytes, metadata);
        final downloadUrl = await uploadTask.ref.getDownloadURL();

        debugPrint('Successfully uploaded $fileName on attempt $attempt');
        return downloadUrl;
      } catch (e) {
        debugPrint('Upload attempt $attempt failed for $fileName: $e');

        if (attempt == maxRetries) {
          return null;
        }

        await Future.delayed(retryDelay * attempt);
      }
    }
    return null;
  }

  @override
  Future<String?> uploadPostVideoMobile(String path, String fileName) async {
    return await _uploadVideoFile(path, videoFolder, fileName);
  }

  @override
  Future<String?> uploadPostVideoWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return await _uploadVideoBytes(fileBytes, videoFolder, fileName);
  }

  // Optimized batch upload with pre-optimization
  @override
  Future<List<String>> uploadMultiplePostImagesMobile(
    List<String> paths,
    String postId,
  ) async {
    if (paths.isEmpty) return [];

    debugPrint(
      'Starting batch upload of ${paths.length} images for post $postId',
    );

    try {
      // Pre-optimize all images first (in parallel)
      // final optimizedPaths = await _preOptimizeImages(paths, postId);
      // Convert path into Typeof OptimizedPath
      final optimizedPaths = paths
          .map((path) => {'index': 0, 'path': path})
          .toList();

      // Upload optimized images in parallel with limited concurrency
      const int batchSize = 3; // Upload 3 at a time to avoid overwhelming
      final List<String> successfulUploads = [];

      for (int i = 0; i < optimizedPaths.length; i += batchSize) {
        final end = (i + batchSize < optimizedPaths.length)
            ? i + batchSize
            : optimizedPaths.length;
        final batch = optimizedPaths.sublist(i, end);

        final batchTasks = batch.map((pathInfo) {
          final index = pathInfo['index'] as int;
          final path = pathInfo['path'] as String;
          final fileName = '${postId}_image_$index.jpg';
          return _uploadFile(path, fileName, postFolder);
        }).toList();

        final batchResults = await Future.wait(batchTasks);
        successfulUploads.addAll(
          batchResults.where((url) => url != null).cast<String>(),
        );
      }

      debugPrint(
        'Successfully uploaded ${successfulUploads.length}/${paths.length} images',
      );

      if (successfulUploads.isEmpty && paths.isNotEmpty) {
        throw Exception('All image uploads failed');
      }

      return successfulUploads;
    } catch (e) {
      debugPrint('Error in batch upload: $e');
      throw e;
    }
  }

  // Pre-optimize images before upload
  Future<List<Map<String, dynamic>>> _preOptimizeImages(
    List<String> paths,
    String postId,
  ) async {
    final List<Map<String, dynamic>> optimizedPaths = [];

    for (int i = 0; i < paths.length; i++) {
      try {
        final cacheKey = '${paths[i]}-${postId}_image_$i';

        // Check cache first
        if (_optimizedCache.containsKey(cacheKey)) {
          optimizedPaths.add({'index': i, 'path': _optimizedCache[cacheKey]});
          continue;
        }

        // Optimize if not in cache
        final optimized = await ImageOptimizationService.optimizeImageFile(
          paths[i],
        );
        final finalPath = optimized?.path ?? paths[i];

        // Cache the result
        _optimizedCache[cacheKey] = finalPath;

        optimizedPaths.add({'index': i, 'path': finalPath});
      } catch (e) {
        debugPrint('Failed to optimize image $i: $e');
        optimizedPaths.add({
          'index': i,
          'path': paths[i], // Use original if optimization fails
        });
      }
    }

    return optimizedPaths;
  }

  @override
  Future<List<String>> uploadMultiplePostImagesWeb(
    List<Uint8List> fileBytes,
    String postId,
  ) async {
    if (fileBytes.isEmpty) return [];

    debugPrint(
      'Starting batch upload of ${fileBytes.length} images for post $postId',
    );

    // Upload images in batches for better performance
    const int batchSize = 3;
    final List<String> successfulUploads = [];

    for (int i = 0; i < fileBytes.length; i += batchSize) {
      final end = (i + batchSize < fileBytes.length)
          ? i + batchSize
          : fileBytes.length;
      final batch = fileBytes.sublist(i, end);

      final batchTasks = <Future<String?>>[];
      for (int j = 0; j < batch.length; j++) {
        final index = i + j;
        final fileName = '${postId}_image_$index.jpg';
        batchTasks.add(_uploadFileBytes(batch[j], fileName, postFolder));
      }

      final batchResults = await Future.wait(batchTasks);
      successfulUploads.addAll(
        batchResults.where((url) => url != null).cast<String>(),
      );
    }

    debugPrint(
      'Successfully uploaded ${successfulUploads.length}/${fileBytes.length} images',
    );

    if (successfulUploads.isEmpty && fileBytes.isNotEmpty) {
      throw Exception('All image uploads failed');
    }

    return successfulUploads;
  }

  Future<String?> _uploadVideoFile(
    String path,
    String folder,
    String fileName,
  ) async {
    try {
      final file = File(path);
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(
        contentType: 'video/mp4',
        cacheControl: 'public, max-age=3600',
      );

      final uploadTask = await storageRef.putFile(file, metadata);
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      debugPrint('Video upload failed: $e');
      return null;
    }
  }

  Future<String?> _uploadVideoBytes(
    Uint8List fileBytes,
    String folder,
    String fileName,
  ) async {
    try {
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(
        contentType: 'video/mp4',
        cacheControl: 'public, max-age=3600',
      );

      final uploadTask = await storageRef.putData(fileBytes, metadata);
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      debugPrint('Video upload failed: $e');
      return null;
    }
  }

  String _getContentType(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'mp4':
        return 'video/mp4';
      default:
        return 'application/octet-stream';
    }
  }

  bool _isImageFile(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(extension);
  }

  // Clear cache when needed (e.g., on logout or memory pressure)
  static void clearOptimizationCache() {
    _optimizedCache.clear();
  }
}
