import 'dart:async';
import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:moneymouthy/features/storage/domain/storage_repo.dart';

class FirebaseStorageRepo implements StorageRepo {
  final FirebaseStorage firebaseStorage = FirebaseStorage.instance;
  final String profileFolder = 'profile_images_new';
  final String postFolder = 'post_images_new';
  final String videoFolder = 'post_videos_new';

  /*
  PROFILE PICTURES - upload profile pictures to storage
  */
  @override
  Future<String?> uploadProfileImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, profileFolder);
  }

  @override
  Future<String?> uploadProfileImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, profileFolder);
  }

  /*
  POST PICTURES - upload post pictures to storage
  */
  @override
  Future<String?> uploadPostImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, postFolder);
  }

  @override
  Future<String?> uploadPostImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, postFolder);
  }

  /// HELPER METHODS - to upload files to storage

  Future<String?> _uploadFile(
    String path,
    String fileName,
    String folder,
  ) async {
    try {
      debugPrint('Upload start: $fileName from $path');
      final file = File(path);
      final exists = await file.exists();
      debugPrint('File exists: $exists');
      if (!exists) {
        debugPrint('ERROR: File does not exist at path: $path');
        throw Exception('File does not exist: $path');
      }

      final size = await file.length();
      debugPrint('File size: $size bytes');
      if (size == 0) {
        debugPrint('ERROR: File is empty: $path');
        throw Exception('File is empty: $path');
      }

      final storageRef = firebaseStorage.ref().child("$folder/$fileName");
      final metadata = SettableMetadata(contentType: _getContentType(fileName));

      debugPrint('Starting Firebase upload for: $fileName');
      debugPrint('Storage path: $folder/$fileName');
      debugPrint('Content type: ${_getContentType(fileName)}');

      final snapshot = await storageRef
          .putFile(file, metadata)
          .timeout(const Duration(seconds: 60)); // Increased timeout

      debugPrint('Firebase upload completed for: $fileName');
      final url = await snapshot.ref.getDownloadURL();
      debugPrint('Download URL obtained: $url');
      return url;
    } on TimeoutException catch (e) {
      debugPrint('ERROR: Upload timeout for $fileName after 60 seconds: $e');
      return null;
    } catch (e) {
      debugPrint('ERROR: Upload failed for $fileName: $e');
      debugPrint('Error type: ${e.runtimeType}');
      if (e.toString().contains('permission') ||
          e.toString().contains('unauthorized')) {
        debugPrint('ERROR: Firebase permission issue detected');
      }
      return null;
    }
  }

  Future<String?> _uploadFileBytes(
    Uint8List fileBytes,
    String fileName,
    String folder,
  ) async {
    try {
      if (fileBytes.isEmpty) {
        throw Exception('File bytes are empty');
      }

      final storageRef = firebaseStorage.ref().child("$folder/$fileName");
      final metadata = SettableMetadata(contentType: _getContentType(fileName));

      final uploadTask = await storageRef.putData(fileBytes, metadata);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Upload failed for $fileName: $e');
      return null;
    }
  }

  @override
  Future<String?> uploadPostVideoMobile(String path, String fileName) async {
    return await _uploadVideoFile(path, videoFolder, fileName);
  }

  @override
  Future<String?> uploadPostVideoWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return await _uploadVideoBytes(fileBytes, videoFolder, fileName);
  }

  @override
  Future<List<String>> uploadMultiplePostImagesMobile(
    List<String> paths,
    String postId,
  ) async {
    if (paths.isEmpty) {
      debugPrint('No paths provided for upload');
      return [];
    }

    debugPrint('=== MOBILE BATCH UPLOAD START ===');
    debugPrint('Post ID: $postId');
    debugPrint('Total images: ${paths.length}');

    for (int i = 0; i < paths.length; i++) {
      debugPrint('Path[$i]: ${paths[i]}');
      // Verify each file exists before starting upload
      final file = File(paths[i]);
      final exists = await file.exists();
      debugPrint('  - File exists: $exists');
      if (exists) {
        final size = await file.length();
        debugPrint('  - File size: $size bytes');
      }
    }

    try {
      const int batchSize = 2; // Reduced batch size for better reliability
      final List<String> successfulUploads = [];
      final List<String> failedPaths = [];

      for (int i = 0; i < paths.length; i += batchSize) {
        final end = (i + batchSize < paths.length)
            ? i + batchSize
            : paths.length;
        final batch = paths.sublist(i, end);

        debugPrint(
          '--- Batch ${(i ~/ batchSize) + 1}/${((paths.length - 1) ~/ batchSize) + 1} ---',
        );
        debugPrint('Uploading ${batch.length} items');

        final batchTasks = batch.asMap().entries.map((entry) {
          final index = i + entry.key;
          final fileName = '${postId}_image_$index.jpg';
          debugPrint('Queuing: ${entry.value} -> $fileName');
          return _uploadFile(entry.value, fileName, postFolder);
        }).toList();

        final batchResults = await Future.wait(batchTasks);

        // Process results
        for (int j = 0; j < batchResults.length; j++) {
          final result = batchResults[j];
          final originalPath = batch[j];
          if (result != null) {
            successfulUploads.add(result);
            debugPrint('✓ Upload successful: $originalPath');
          } else {
            failedPaths.add(originalPath);
            debugPrint('✗ Upload failed: $originalPath');
          }
        }

        debugPrint(
          'Batch result: ${batchResults.whereType<String>().length}/${batch.length} succeeded',
        );

        // Add small delay between batches to avoid overwhelming Firebase
        if (i + batchSize < paths.length) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      debugPrint('=== MOBILE BATCH UPLOAD COMPLETE ===');
      debugPrint(
        'Successful uploads: ${successfulUploads.length}/${paths.length}',
      );
      debugPrint('Failed uploads: ${failedPaths.length}');

      if (failedPaths.isNotEmpty) {
        debugPrint('Failed paths:');
        for (final path in failedPaths) {
          debugPrint('  - $path');
        }
      }

      if (successfulUploads.isEmpty && paths.isNotEmpty) {
        throw Exception('All ${paths.length} image uploads failed');
      }

      return successfulUploads;
    } catch (e) {
      debugPrint('ERROR in batch upload: $e');
      debugPrint('Error type: ${e.runtimeType}');
      rethrow;
    }
  }

  @override
  Future<List<String>> uploadMultiplePostImagesWeb(
    List<Uint8List> fileBytes,
    String postId,
  ) async {
    if (fileBytes.isEmpty) return [];

    const int batchSize = 3;
    final List<String> successfulUploads = [];

    for (int i = 0; i < fileBytes.length; i += batchSize) {
      final end = (i + batchSize < fileBytes.length)
          ? i + batchSize
          : fileBytes.length;
      final batch = fileBytes.sublist(i, end);

      final batchTasks = batch.asMap().entries.map((entry) {
        final index = i + entry.key;
        final fileName = '${postId}_image_$index.jpg';
        return _uploadFileBytes(entry.value, fileName, postFolder);
      }).toList();

      final batchResults = await Future.wait(batchTasks);
      successfulUploads.addAll(batchResults.whereType<String>());
    }

    if (successfulUploads.isEmpty && fileBytes.isNotEmpty) {
      throw Exception('All image uploads failed');
    }

    return successfulUploads;
  }

  Future<String?> _uploadVideoFile(
    String path,
    String folder,
    String fileName,
  ) async {
    try {
      final file = File(path);
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(contentType: 'video/mp4');

      final uploadTask = await storageRef.putFile(file, metadata);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Video upload failed: $e');
      return null;
    }
  }

  Future<String?> _uploadVideoBytes(
    Uint8List fileBytes,
    String folder,
    String fileName,
  ) async {
    try {
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(contentType: 'video/mp4');

      final uploadTask = await storageRef.putData(fileBytes, metadata);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Video upload failed: $e');
      return null;
    }
  }

  String _getContentType(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'mp4':
        return 'video/mp4';
      default:
        return 'application/octet-stream';
    }
  }
}
