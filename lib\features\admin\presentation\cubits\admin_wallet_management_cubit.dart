import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/admin/domain/repos/admin_wallet_management_repo.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_wallet_management_state.dart';
import 'package:moneymouthy/features/wallet/domain/entities/wallet.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart';

class AdminWalletManagementCubit extends Cubit<AdminWalletManagementState> {
  final AdminWalletManagementRepo walletManagementRepo;

  AdminWalletManagementCubit({required this.walletManagementRepo})
    : super(AdminWalletManagementInitial());

  // Load all user wallets
  Future<void> loadAllUserWallets() async {
    emit(AdminWalletManagementLoading());
    try {
      final wallets = await walletManagementRepo.getAllUserWallets();
      emit(AdminWalletManagementWalletsLoaded(wallets: wallets));
    } catch (e) {
      emit(AdminWalletManagementError('Failed to load wallets: $e'));
    }
  }

  // Get wallet by user ID
  Future<void> getWalletByUserId(String userId) async {
    emit(AdminWalletManagementLoading());
    try {
      final wallet = await walletManagementRepo.getWalletByUserId(userId);
      if (wallet != null) {
        emit(AdminWalletManagementWalletSelected(wallet: wallet));
      } else {
        emit(AdminWalletManagementError('Wallet not found'));
      }
    } catch (e) {
      emit(AdminWalletManagementError('Failed to get wallet: $e'));
    }
  }

  // Add balance to user wallet
  Future<void> addBalanceToUserWallet(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      await walletManagementRepo.addBalanceToUserWallet(
        userId,
        amount,
        description,
      );

      // Reload wallets to reflect changes
      if (state is AdminWalletManagementWalletsLoaded) {
        await loadAllUserWallets();
      }
    } catch (e) {
      emit(AdminWalletManagementError('Failed to add balance: $e'));
    }
  }

  // Deduct balance from user wallet
  Future<void> deductBalanceFromUserWallet(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      await walletManagementRepo.deductBalanceFromUserWallet(
        userId,
        amount,
        description,
      );

      // Reload wallets to reflect changes
      if (state is AdminWalletManagementWalletsLoaded) {
        await loadAllUserWallets();
      }
    } catch (e) {
      emit(AdminWalletManagementError('Failed to deduct balance: $e'));
    }
  }

  // Get user transaction history
  Future<void> getUserTransactionHistory(String userId) async {
    emit(AdminWalletManagementLoading());
    try {
      final transactions = await walletManagementRepo.getUserTransactionHistory(
        userId,
      );
      emit(AdminWalletManagementTransactionsLoaded(transactions: transactions));
    } catch (e) {
      emit(AdminWalletManagementError('Failed to get transaction history: $e'));
    }
  }

  // Get all transactions (admin view)
  Future<void> getAllTransactions() async {
    emit(AdminWalletManagementLoading());
    try {
      final transactions = await walletManagementRepo.getAllTransactions();
      emit(
        AdminWalletManagementAllTransactionsLoaded(transactions: transactions),
      );
    } catch (e) {
      emit(AdminWalletManagementError('Failed to get all transactions: $e'));
    }
  }

  // Get wallet statistics
  Future<void> getWalletStatistics() async {
    try {
      final stats = await walletManagementRepo.getWalletStatistics();
      emit(AdminWalletManagementStatsLoaded(stats: stats));
    } catch (e) {
      emit(AdminWalletManagementError('Failed to get wallet statistics: $e'));
    }
  }

  // Bulk add balance to all users
  Future<void> bulkAddBalanceToAllUsers(
    double amount,
    String description,
  ) async {
    emit(AdminWalletManagementLoading());
    try {
      await walletManagementRepo.bulkAddBalanceToAllUsers(amount, description);

      // Reload wallets to reflect changes
      await loadAllUserWallets();
    } catch (e) {
      emit(AdminWalletManagementError('Failed to bulk add balance: $e'));
    }
  }

  // Clear error state
  void clearError() {
    if (state is AdminWalletManagementError) {
      emit(AdminWalletManagementInitial());
    }
  }
}
