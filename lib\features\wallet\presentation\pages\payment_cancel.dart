import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';

import '../../../../responsive/constrained_scaffold.dart';

class PaymentCancel extends StatelessWidget {
  // cotext and state
  final GoRouterState state;

  const PaymentCancel({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      appBar: AppBar(
        title: const Text('Payment Cancelled'),
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: () {
            final authState = context.read<AuthCubit>().state;
            if (authState is Authenticated) {
              context.go('/home');
            } else {
              context.go(kIsWeb ? '/landing' : '/auth');
            }
          },
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.cancel, color: Colors.orange, size: 80),
            const SizedBox(height: 16),
            const Text(
              'Payment Cancelled',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                final authState = context.read<AuthCubit>().state;
                if (authState is Authenticated) {
                  context.go('/home');
                } else {
                  context.go(kIsWeb ? '/landing' : '/auth');
                }
              },
              child: const Text('Continue'),
            ),
          ],
        ),
      ),
    );
  }
}
