import 'package:flutter/material.dart';
import 'package:moneymouthy/features/poll/domain/entities/poll.dart';

class PollCreationWidget extends StatefulWidget {
  final Function(String question) onPollCreated;
  final VoidCallback? onCancel;

  const PollCreationWidget({
    super.key,
    required this.onPollCreated,
    this.onCancel,
  });

  @override
  State<PollCreationWidget> createState() => _PollCreationWidgetState();
}

class _PollCreationWidgetState extends State<PollCreationWidget> {
  final TextEditingController _questionController = TextEditingController();
  final int _maxQuestionLength = 200;

  @override
  void dispose() {
    _questionController.dispose();
    super.dispose();
  }

  void _createPoll() {
    final question = _questionController.text.trim();
    if (question.isNotEmpty) {
      widget.onPollCreated(question);
      _questionController.clear();
    }
  }

  // on change listener and update btton state
  void _onQuestionChanged() {
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _questionController.addListener(_onQuestionChanged);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Question input section
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Theme.of(
              context,
            ).colorScheme.surfaceContainerHighest.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.help_outline,
                    color: Colors.purple.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Question',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.purple.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _questionController,
                maxLines: 3,
                maxLength: _maxQuestionLength,
                textInputAction: TextInputAction.done,
                decoration: InputDecoration(
                  hintText: 'What would you like to ask?',
                  hintStyle: TextStyle(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.6),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(
                        context,
                      ).colorScheme.outline.withOpacity(0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.purple.shade700,
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  contentPadding: const EdgeInsets.all(16),
                  counterText: '',
                ),
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 8),
              Text(
                '${_questionController.text.length}/$_maxQuestionLength',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withOpacity(0.6),
                ),
                textAlign: TextAlign.right,
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Poll options preview section
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Theme.of(
              context,
            ).colorScheme.surfaceContainerHighest.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.list_alt, color: Colors.purple.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Poll Options',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.purple.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Your poll will have these 3 options:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 12),

              // Options display
              ...PollOption.values.map(
                (option) => Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    border: Border.all(
                      color: _getOptionColor(option).withOpacity(0.3),
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getOptionColor(option).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          option == PollOption.yes
                              ? Icons.thumb_up
                              : option == PollOption.no
                              ? Icons.thumb_down
                              : Icons.remove,
                          color: _getOptionColor(option),
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        option.displayName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getOptionColor(option).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '0%',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: _getOptionColor(option),
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Create button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _questionController.text.trim().isNotEmpty
                  ? _createPoll
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _questionController.text.trim().isNotEmpty
                    ? Colors.purple.shade700
                    : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                foregroundColor: _questionController.text.trim().isNotEmpty
                    ? Colors.white
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: _questionController.text.trim().isNotEmpty ? 2 : 0,
              ),
              child: Text(
                'Create Poll',
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),
      ],
    );
  }

  Color _getOptionColor(PollOption option) {
    switch (option) {
      case PollOption.yes:
        return Colors.green;
      case PollOption.no:
        return Colors.red;
      case PollOption.dontCare:
        return Colors.orange;
    }
  }
}

// Simple poll preview widget for upload post page
class PollPreviewWidget extends StatelessWidget {
  final String question;
  final VoidCallback? onRemove;

  const PollPreviewWidget({super.key, required this.question, this.onRemove});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.poll, color: Colors.purple.shade700, size: 20),
              const SizedBox(width: 4),
              Text(
                'Poll',
                style: TextStyle(color: Colors.purple.shade700, fontSize: 10),
              ),
              const Spacer(),
              if (onRemove != null)
                IconButton(
                  onPressed: onRemove,
                  icon: const Icon(Icons.close),
                  iconSize: 12,
                  color: Colors.red,
                  constraints: const BoxConstraints(
                    minWidth: 20,
                    minHeight: 20,
                  ),
                ),
            ],
          ),
          // const SizedBox(height: 2),
          // Text(
          //   question,
          //   style: Theme.of(
          //     context,
          //   ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          // ),
          // const SizedBox(height: 2),
          // Text(
          //   'Options: Yes, No, Don\'t Care',
          //   style: Theme.of(context).textTheme.bodySmall?.copyWith(
          //     color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          //   ),
          // ),
        ],
      ),
    );
  }
}
