# Firebase Cloud Functions

This directory contains Firebase Cloud Functions for the MoneyMouthy app.

## Functions Overview

### Payment Functions

- `createPaymentIntent` - Creates Stripe payment intents
- `handlePaymentCompletion` - Processes completed payments and updates wallet balances
- `createCheckoutSession` - Creates Stripe checkout sessions
- `handleStripeWebhook` - Handles Stripe webhooks for automatic payment processing

### Utility Functions

- `proxyRequest` - Proxies external requests (for image search, etc.)

### Cleanup Functions

- `cleanupOldPostsAndMedia` - **NEW**: Automatically deletes posts and media older than 48 hours
- `manualCleanup` - **NEW**: Manual cleanup function for testing/admin use

## Auto Cleanup System

### How It Works

The `cleanupOldPostsAndMedia` function runs automatically every hour and:

1. Finds posts older than 48 hours in the `posts_new` collection
2. Extracts all associated media URLs (images and videos)
3. Deletes media files from Firebase Storage
4. Deletes posts from Firestore
5. Deletes associated polls from the `polls` collection

### Storage Structure

Media files are organized in Firebase Storage as:

- `profile_images_new/` - User profile pictures
- `post_images_new/` - Post images (multiple per post)
- `post_videos_new/` - Post videos

### Database Collections

- `users` - User profiles
- `posts_new` - Posts with media URLs
- `wallets_new` - User wallet balances
- `transactions_new` - Transaction history
- `polls` - Poll data
- `admins` - Admin users

## Deployment

### Prerequisites

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`
3. Initialize project: `firebase init` (if not done)

### Deploy Functions

```bash
cd functions
npm install
firebase deploy --only functions
```

### Schedule Cleanup Function

The cleanup function is scheduled to run every hour. To enable it:

1. Deploy the function
2. Go to Google Cloud Console > Cloud Scheduler
3. The function will be automatically scheduled

### Test Manual Cleanup

#### Option A: Simple GET Request (No Authentication - Easiest)

```bash
# Using browser - simplest method
https://your-region-your-project.cloudfunctions.net/manualCleanup

# Or using curl
curl https://your-region-your-project.cloudfunctions.net/manualCleanup
```

#### Option B: POST Request with Custom Hours

```bash
# Using curl with custom retention period
curl -X POST https://your-region-your-project.cloudfunctions.net/manualCleanup \
  -H "Content-Type: application/json" \
  -d '{"hours": 24}'
```

#### Option C: With API Key Authentication (if configured)

```bash
# Set API key first
firebase functions:config:set cleanup.api_key="your_secret_key"

# Then use with authentication
curl -X POST https://your-region-your-project.cloudfunctions.net/manualCleanup \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_secret_key" \
  -d '{"hours": 48}'
```

#### Troubleshooting "Forbidden" Error:

1. **Try GET request first**: Use the browser URL method above
2. **Check function deployment**: Ensure functions are deployed successfully
3. **Verify project URL**: Make sure you're using the correct region and project ID
4. **Check Firebase Console**: Look at function logs for any authentication errors

## Environment Variables

Set these in Firebase Functions config:

```bash
firebase functions:config:set stripe.secret_key="your_stripe_secret_key"
```

## Monitoring

- View function logs: `firebase functions:log`
- Monitor cleanup activity in Firebase Console
- Check storage usage reduction over time

## Security Notes

- All functions include CORS headers
- Payment functions validate input thoroughly
- Cleanup function only deletes data older than 48 hours
- Manual cleanup supports both GET (for testing) and POST requests
- Optional API key authentication available for production use
- Scheduled cleanup runs with Firebase Admin privileges automatically
