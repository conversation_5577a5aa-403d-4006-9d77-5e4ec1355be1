import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_cubit.dart';
import 'package:moneymouthy/features/post/presentation/pages/post_detail_page.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';

/// Wrapper widget to handle async post loading
class PostDetailWrapper extends StatefulWidget {
  final String postId;

  const PostDetailWrapper({super.key, required this.postId});

  @override
  State<PostDetailWrapper> createState() => _PostDetailWrapperState();
}

class _PostDetailWrapperState extends State<PostDetailWrapper> {
  late Future<Post> _postFuture;

  @override
  void initState() {
    super.initState();
    _postFuture = _loadPost();
  }

  Future<Post> _loadPost() async {
    try {
      final postCubit = context.read<PostCubit>();
      final post = await postCubit.getPostById(widget.postId);
      return post;
    } catch (e) {
      return Post.empty();
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Post>(
      future: _postFuture,
      builder: (context, snapshot) {
        // Loading state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const ConstrainedScaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  LoadingIndicator(),
                  SizedBox(height: 16),
                  Text('Loading post...'),
                ],
              ),
            ),
          );
        }

        // Error state or no data
        if (snapshot.hasError || !snapshot.hasData) {
          return PostNotFoundPage(
            onNavigateHome: () => _navigateToHome(context),
          );
        }

        final post = snapshot.data!;

        // Empty post (post not found)
        if (post.id.isEmpty) {
          return PostNotFoundPage(
            onNavigateHome: () => _navigateToHome(context),
          );
        }

        // Success - show the post
        return PostDetailPage(post: post);
      },
    );
  }

  void _navigateToHome(BuildContext context) {
    if (context.mounted) {
      context.go('/home');
    }
  }
}

/// Widget to show when post is not found
class PostNotFoundPage extends StatefulWidget {
  final VoidCallback onNavigateHome;

  const PostNotFoundPage({super.key, required this.onNavigateHome});

  @override
  State<PostNotFoundPage> createState() => _PostNotFoundPageState();
}

class _PostNotFoundPageState extends State<PostNotFoundPage> {
  int _countdown = 3;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 1) {
        setState(() {
          _countdown--;
        });
      } else {
        timer.cancel();
        widget.onNavigateHome();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'Post not found',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Redirecting to home in $_countdown seconds...',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: widget.onNavigateHome,
              child: const Text('Go to Home Now'),
            ),
          ],
        ),
      ),
    );
  }
}
