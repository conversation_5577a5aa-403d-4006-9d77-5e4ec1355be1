import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_stats_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_stats_state.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_user_management_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_user_management_state.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_wallet_management_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:toastification/toastification.dart';

class AdminToolsPage extends StatefulWidget {
  const AdminToolsPage({super.key});

  @override
  State<AdminToolsPage> createState() => _AdminToolsPageState();
}

class _AdminToolsPageState extends State<AdminToolsPage> {
  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      appBar: AppBar(title: const Text('Admin Tools'), centerTitle: true),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // System Management Section
            _buildSectionHeader(
              context,
              'Bulk Management',
              Icons.settings_system_daydream,
            ),
            _buildSystemManagementTools(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary, size: 28),
        const SizedBox(width: 12),
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildSystemManagementTools(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildToolButton(
              context,
              'Mutual Follow',
              'Make the users follow each other',
              Icons.health_and_safety,
              Colors.green,
              () => _showMutualFollowDialog(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withAlpha(50),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showMutualFollowDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Mutual Follow'),
        content: const Text('This will make all users follow each other.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await context
                  .read<ProfileCubit>()
                  .profileRepo
                  .makeAllUsersMutuallyFollow();
              // Perform mutual follow
              Navigator.of(context).pop();
              // use toastification
              toastification.show(
                alignment: Alignment.topCenter,
                title: Text('Success'),
                description: Text('Mutual follow completed successfully'),
                type: ToastificationType.success,
                style: ToastificationStyle.flatColored,
                autoCloseDuration: Duration(seconds: 3),
              );
            },
            child: const Text('Proceed'),
          ),
        ],
      ),
    );
  }
}
