import 'package:flutter/material.dart';
import 'package:moneymouthy/features/poll/domain/entities/poll.dart';

class PollDisplayWidget extends StatelessWidget {
  final Poll poll;
  final String? currentUserId;
  final Function(PollOption)? onVote;
  final bool showResults;
  final bool isInteractive;

  const PollDisplayWidget({
    super.key,
    required this.poll,
    this.currentUserId,
    this.onVote,
    this.showResults = true,
    this.isInteractive = true,
  });

  @override
  Widget build(BuildContext context) {
    final hasUserVoted =
        currentUserId != null && poll.hasUserVoted(currentUserId!);
    final userVote = currentUserId != null
        ? poll.getUserVote(currentUserId!)
        : null;

    return Container(
      padding: const EdgeInsets.all(8), // Reduced padding for compactness
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(
          8,
        ), // Reduced border radius for a sleeker look
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Poll header
          Row(
            children: [
              Icon(
                Icons.poll,
                color: Colors.purple.shade700,
                size: 18, // Slightly smaller icon
              ),
              const SizedBox(width: 6), // Reduced width for header
              Text(
                'Poll',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.purple.shade700,
                ),
              ),
              const Spacer(),
              Text(
                '${poll.totalVotes} votes',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8), // Reduced spacing for question
          // Question
          Text(
            poll.question,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(
            height: 8,
          ), // Reduced spacing between question and options
          // Poll options in a single line (horizontally)
          Row(
            children: PollOption.values
                .map(
                  (option) => Expanded(
                    child: _buildPollOption(
                      context,
                      option,
                      hasUserVoted,
                      userVote,
                    ),
                  ),
                )
                .toList(),
          ),

          // Poll info
          if (poll.isExpired) ...[
            const SizedBox(height: 8), // Reduced spacing for expired info
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 6,
                vertical: 2,
              ), // Reduced padding
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Poll Expired',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPollOption(
    BuildContext context,
    PollOption option,
    bool hasUserVoted,
    PollOption? userVote,
  ) {
    final voteCount = poll.getVotesForOption(option);
    final percentage = poll.getPercentageForOption(option);
    final isSelected = userVote == option;
    final canVote = isInteractive && !poll.isExpired && onVote != null;

    return InkWell(
      onTap: canVote ? () => onVote!(option) : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: 4,
        ), // Reduced margin for compact layout
        padding: const EdgeInsets.all(4), // Reduced padding for compactness
        decoration: BoxDecoration(
          color: isSelected
              ? _getOptionColor(option).withOpacity(0.2)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? _getOptionColor(option)
                : Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            // Option indicator
            Row(
              children: [
                Container(
                  width: 14,
                  height: 14,
                  decoration: BoxDecoration(
                    color: _getOptionColor(option),
                    shape: BoxShape.circle,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 10)
                      : null,
                ),
                const SizedBox(width: 8), // Reduced width
                // Option text
                Expanded(
                  child: Text(
                    option.displayName,
                    style: TextStyle(
                      fontSize: 12, // Smaller font size
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),

            // Vote count and percentage
            if (showResults) ...[
              Row(
                children: [
                  Text(
                    '$voteCount',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 4), // Reduced spacing
                  Text(
                    '${percentage.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getOptionColor(PollOption option) {
    switch (option) {
      case PollOption.yes:
        return Colors.green;
      case PollOption.no:
        return Colors.red;
      case PollOption.dontCare:
        return Colors.orange;
    }
  }
}

// Compact poll display for post tiles
class CompactPollDisplayWidget extends StatelessWidget {
  final Poll poll;
  final String? currentUserId;
  final Function(PollOption)? onVote;

  const CompactPollDisplayWidget({
    super.key,
    required this.poll,
    this.currentUserId,
    this.onVote,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.poll, color: Colors.purple.shade700, size: 16),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  poll.question,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${poll.totalVotes} votes • Tap to view details',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }
}
