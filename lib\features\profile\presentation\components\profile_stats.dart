/* *
PROFILE STATS

This widget is used to display the profile stats
of a user.

-----------------------------------------------
Profile Stats
  - posts
  - followers
  - following
*/

import 'package:flutter/material.dart';

class ProfileStats extends StatelessWidget {
  final int postCount;
  final int followerCount;
  final int followingCount;
  final int unfollowingCount;
  final void Function()? onTap;

  const ProfileStats({
    super.key,
    required this.postCount,
    required this.followerCount,
    required this.followingCount,
    required this.unfollowingCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // text style for count
    var textStyleForCount = TextStyle(
      color: Theme.of(context).colorScheme.inversePrimary,
      fontSize: 20,
    );
    // text style for label
    var textStyleForLabel = TextStyle(
      color: Theme.of(context).colorScheme.primary,
      fontSize: 13,
    );
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Row(
          children: [
            // posts
            Expanded(
              child: Column(
                children: [
                  Text(postCount.toString(), style: textStyleForCount),
                  Text('Posts', style: textStyleForLabel),
                ],
              ),
            ),
            // followers
            Expanded(
              child: Column(
                children: [
                  Text(followerCount.toString(), style: textStyleForCount),
                  Text('Followers', style: textStyleForLabel),
                ],
              ),
            ),
            // following
            Expanded(
              child: Column(
                children: [
                  Text(followingCount.toString(), style: textStyleForCount),
                  Text("Following", style: textStyleForLabel),
                ],
              ),
            ),
            // unfollowing
            Expanded(
              child: Column(
                children: [
                  Text(unfollowingCount.toString(), style: textStyleForCount),
                  Text('Unfollowing', style: textStyleForLabel),
                ],
              ),
            ),
          ],
        ),
      
      ),
    );
  }
}
