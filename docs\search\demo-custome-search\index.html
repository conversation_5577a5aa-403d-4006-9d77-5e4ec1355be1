<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Image Search API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .search-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .stats {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            border-left: 4px solid #2196f3;
        }

        .stats h3 {
            color: #1976d2;
            margin-bottom: 5px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .page-btn {
            background: #fff;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .page-btn:hover, .page-btn.active {
            background: #667eea;
            color: white;
            transform: scale(1.05);
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .image-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .image-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .image-wrapper {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .image-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .image-card:hover .image-wrapper img {
            transform: scale(1.1);
        }

        .image-info {
            padding: 20px;
        }

        .image-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .image-source {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .image-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .view-btn {
            background: #4caf50;
            color: white;
        }

        .view-btn:hover {
            background: #45a049;
        }

        .download-btn {
            background: #2196f3;
            color: white;
        }

        .download-btn:hover {
            background: #1976d2;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #f44336;
        }

        .no-results {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .images-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 20px;
            }
            
            .pagination {
                gap: 5px;
            }
            
            .page-btn {
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Google Image Search API</h1>
            <p>Search and discover images with advanced pagination</p>
        </div>

        <div class="search-section">
            <div class="form-group">
                <label for="query">Search Query:</label>
                <input type="text" id="query" placeholder="Enter your search query..." value="Russia and USA ships starting war?">
            </div>
            
            <div class="form-group">
                <label for="apiKey">API Key:</label>
                <input type="text" id="apiKey" placeholder="Enter your Google API key..." value="AIzaSyBuWIljmmOuiCZoFOOse71pWIE4x0CBG20">
            </div>
            
            <div class="form-group">
                <label for="searchEngineId">Search Engine ID:</label>
                <input type="text" id="searchEngineId" placeholder="Enter your Custom Search Engine ID..." value="5373b9df27ab44ae2">
            </div>
            
            <button class="search-btn" onclick="searchImages()">🔍 Search Images</button>
        </div>

        <div id="stats" class="stats" style="display: none;">
            <h3>Search Results</h3>
            <p id="statsText">Found results for your query</p>
        </div>

        <div id="pagination" class="pagination" style="display: none;"></div>

        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>Searching for images...</p>
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="images" class="images-grid"></div>
    </div>

    <script>
        let currentPage = 1;
        let currentQuery = '';
        let totalResults = 0;
        const resultsPerPage = 10;
        const maxPages = 5; // Google allows up to 50 results (5 pages of 10)

        async function searchImages(page = 1) {
            const query = document.getElementById('query').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const searchEngineId = document.getElementById('searchEngineId').value.trim();

            if (!query || !apiKey || !searchEngineId) {
                showError('Please fill in all fields.');
                return;
            }

            currentQuery = query;
            currentPage = page;

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('images').innerHTML = '';
            document.getElementById('stats').style.display = 'none';
            document.getElementById('pagination').style.display = 'none';

            // Calculate start index for pagination
            const startIndex = (page - 1) * resultsPerPage + 1;

            const url = `https://www.googleapis.com/customsearch/v1?searchType=image&num=${resultsPerPage}&key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&start=${startIndex}`;

            try {
                const response = await fetch(url);
                const data = await response.json();

                document.getElementById('loading').style.display = 'none';

                if (data.error) {
                    showError(`API Error: ${data.error.message}`);
                    return;
                }

                if (!data.items || data.items.length === 0) {
                    document.getElementById('images').innerHTML = '<div class="no-results">No images found for your search query.</div>';
                    return;
                }

                // Update total results
                totalResults = parseInt(data.searchInformation.totalResults) || 0;

                // Show stats
                showStats(data);

                // Show pagination
                showPagination();

                // Display images
                displayImages(data.items);

            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                showError(`Network error: ${error.message}`);
            }
        }

        function showStats(data) {
            const statsDiv = document.getElementById('stats');
            const statsText = document.getElementById('statsText');
            
            const resultCount = data.items.length;
            const searchTime = data.searchInformation.searchTime;
            
            statsText.innerHTML = `
                Found ${resultCount} images on page ${currentPage} 
                (${totalResults.toLocaleString()} total results in ${searchTime} seconds)
            `;
            
            statsDiv.style.display = 'block';
        }

        function showPagination() {
            const paginationDiv = document.getElementById('pagination');
            const maxPossiblePages = Math.min(maxPages, Math.ceil(Math.min(totalResults, 50) / resultsPerPage));
            
            if (maxPossiblePages <= 1) {
                paginationDiv.style.display = 'none';
                return;
            }

            let paginationHTML = '';

            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button class="page-btn" onclick="searchImages(${currentPage - 1})">← Previous</button>`;
            }

            // Page numbers
            for (let i = 1; i <= maxPossiblePages; i++) {
                const activeClass = i === currentPage ? ' active' : '';
                paginationHTML += `<button class="page-btn${activeClass}" onclick="searchImages(${i})">${i}</button>`;
            }

            // Next button
            if (currentPage < maxPossiblePages) {
                paginationHTML += `<button class="page-btn" onclick="searchImages(${currentPage + 1})">Next →</button>`;
            }

            paginationDiv.innerHTML = paginationHTML;
            paginationDiv.style.display = 'flex';
        }

        function displayImages(images) {
            const imagesDiv = document.getElementById('images');
            
            const imagesHTML = images.map((image, index) => {
                const title = image.title || 'Untitled Image';
                const displayLink = image.displayLink || 'Unknown Source';
                const contextLink = image.image.contextLink || '#';
                const thumbnailLink = image.link || image.image.thumbnailLink;
                
                return `
                    <div class="image-card">
                        <div class="image-wrapper">
                            <img src="${thumbnailLink}" 
                                 alt="${title}" 
                                 loading="lazy"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIE5vdCBGb3VuZDwvdGV4dD48L3N2Zz4='">
                        </div>
                        <div class="image-info">
                            <div class="image-title">${title}</div>
                            <div class="image-source">Source: ${displayLink}</div>
                            <div class="image-actions">
                                <button class="action-btn view-btn" onclick="viewImage('${contextLink}')">👁️ View</button>
                                <button class="action-btn download-btn" onclick="downloadImage('${image.link}', '${title}')">⬇️ Download</button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            imagesDiv.innerHTML = imagesHTML;
        }

        function viewImage(url) {
            if (url && url !== '#') {
                window.open(url, '_blank');
            }
        }

        function downloadImage(imageUrl, title) {
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Allow Enter key to trigger search
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchImages();
                    }
                });
            });
        });

        // Auto-search on page load if all fields are filled
        window.addEventListener('load', function() {
            const query = document.getElementById('query').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const searchEngineId = document.getElementById('searchEngineId').value.trim();
            
            if (query && apiKey && searchEngineId) {
                searchImages();
            }
        });
    </script>
</body>
</html>