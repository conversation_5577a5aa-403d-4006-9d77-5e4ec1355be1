/*

Admin States
- checking is admin
- is admin
- is not admin
- loaded admin 


*/

import 'package:flutter/material.dart';
import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';
import 'package:toastification/toastification.dart';

abstract class AdminState {}

// initial Checking state
class AdminInitial extends AdminState {}

// Admin is not verified
class AdminNotVerified extends AdminState {}

// Admin is verified
class AdminVerified extends AdminState {
  final AppUser user;

  AdminVerified(this.user);
}

// Admin is loading
class AdminLoading extends AdminState {}

// admin error

class AdminError extends AdminState {
  final String message;

  AdminError(this.message) {
    // show error message in toastification
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text('Admin Error'),
      description: Text(message),
      type: ToastificationType.error,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: Duration(seconds: 3),
    );
  }
}
