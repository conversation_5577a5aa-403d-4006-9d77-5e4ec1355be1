// lib/features/post/presentation/components/image_search_bottom_sheet.dart
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/post/presentation/services/image_search_service.dart';
import 'package:moneymouthy/features/post/presentation/services/media_selection_service.dart';
import 'package:toastification/toastification.dart';

class ImageSearchBottomSheet extends StatefulWidget {
  final Function(List<MediaFile>) onImagesSelected;

  const ImageSearchBottomSheet({super.key, required this.onImagesSelected});

  static void show(
    BuildContext context, {
    required Function(List<MediaFile>) onImagesSelected,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) =>
          ImageSearchBottomSheet(onImagesSelected: onImagesSelected),
    );
  }

  @override
  State<ImageSearchBottomSheet> createState() => _ImageSearchBottomSheetState();
}

class _ImageSearchBottomSheetState extends State<ImageSearchBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<String> _searchedImageUrls = [];
  List<String> _selectedSearchImageUrls = [];
  bool _isSearchingImages = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performImageSearch() async {
    if (_searchController.text.trim().isEmpty) {
      _showToast(
        title: 'Search Required',
        description: 'Please enter a search term',
        type: ToastificationType.info,
      );
      return;
    }

    setState(() {
      _isSearchingImages = true;
    });

    try {
      final results = await ImageSearchService.searchImages(
        _searchController.text.trim(),
      );
      setState(() {
        _searchedImageUrls.clear();
        _searchedImageUrls.addAll(results);
        _selectedSearchImageUrls.clear();
      });
    } catch (e) {
      _showToast(
        title: 'Search Failed',
        description: 'Failed to search images: ${e.toString()}',
        type: ToastificationType.error,
      );
    } finally {
      setState(() {
        _isSearchingImages = false;
      });
    }
  }

  Future<void> _addSelectedImages() async {
    if (_selectedSearchImageUrls.isEmpty) return;

    // Show loading state
    setState(() {
      _isSearchingImages = true;
    });

    try {
      final mediaFiles = await ImageSearchService.downloadImagesAsMediaFiles(
        _selectedSearchImageUrls,
      );

      if (mediaFiles.isNotEmpty) {
        widget.onImagesSelected(mediaFiles);

        final selectedCount = _selectedSearchImageUrls.length;
        final downloadedCount = mediaFiles.length;

        if (mounted) {
          Navigator.pop(context);

          if (downloadedCount == selectedCount) {
            _showToast(
              title: 'Images Added',
              description: '$downloadedCount image(s) added successfully',
              type: ToastificationType.success,
            );
          } else {
            _showToast(
              title: 'Partial Success',
              description:
                  '$downloadedCount of $selectedCount image(s) added successfully. Some images failed to download.',
              type: ToastificationType.warning,
            );
          }
        }
      } else {
        _showToast(
          title: 'Download Failed',
          description:
              'Failed to download any of the selected images. Please check your internet connection and try again.',
          type: ToastificationType.error,
        );
      }
    } catch (e) {
      _showToast(
        title: 'Error',
        description: 'Failed to add images: ${e.toString()}',
        type: ToastificationType.error,
      );
    } finally {
      setState(() {
        _isSearchingImages = false;
      });
    }
  }

  void _toggleImageSelection(String url) {
    setState(() {
      if (_selectedSearchImageUrls.contains(url)) {
        _selectedSearchImageUrls.remove(url);
      } else if (_selectedSearchImageUrls.length < 5) {
        _selectedSearchImageUrls.add(url);
      } else {
        _showToast(
          title: 'Selection Limit',
          description: 'You can select up to 5 images',
          type: ToastificationType.info,
        );
      }
    });
  }

  void _showToast({
    required String title,
    required String description,
    required ToastificationType type,
  }) {
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text(title),
      description: Text(description),
      type: type,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: const Duration(seconds: 3),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) => Column(
        children: [
          _buildHeader(),
          _buildSearchInput(),
          Expanded(child: _buildContent(scrollController)),
          if (_selectedSearchImageUrls.isNotEmpty) _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Text('Search Images', style: Theme.of(context).textTheme.titleLarge),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchInput() {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        16.0,
        16.0,
        16.0,
        MediaQuery.of(context).viewInsets.bottom + 16.0,
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search for images...',
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _isSearchingImages
              ? const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: LoadingIndicator(strokeWidth: 2),
                  ),
                )
              : IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: _performImageSearch,
                ),
        ),
        onSubmitted: (_) => _performImageSearch(),
      ),
    );
  }

  Widget _buildContent(ScrollController scrollController) {
    if (_isSearchingImages) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicator(),
            SizedBox(height: 16),
            Text('Searching for images...'),
          ],
        ),
      );
    }

    if (_searchedImageUrls.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.image_search, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Search for images to display results',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      controller: scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.0,
      ),
      itemCount: _searchedImageUrls.length,
      itemBuilder: (context, index) {
        final url = _searchedImageUrls[index];
        final isSelected = _selectedSearchImageUrls.contains(url);

        return GestureDetector(
          onTap: () => _toggleImageSelection(url),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey.shade300,
                width: isSelected ? 3 : 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    child: CachedNetworkImage(
                      imageUrl: url,
                      imageBuilder: (context, imageProvider) => Container(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      placeholder: (context, url) => Container(
                        color: Colors.grey.shade100,
                        child: const Center(child: LoadingIndicator()),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey.shade200,
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.broken_image, color: Colors.grey),
                            SizedBox(height: 4),
                            Text(
                              'Failed to load, Avoid selecting this image',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (isSelected)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            '${_selectedSearchImageUrls.length} image(s) selected',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _addSelectedImages,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.tertiary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Add Selected Images',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
