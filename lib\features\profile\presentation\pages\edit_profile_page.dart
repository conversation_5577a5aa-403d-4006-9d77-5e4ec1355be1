import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/auth/presentation/components/my_text_field.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:toastification/toastification.dart';

import '../../domain/entities/profile_user.dart';

class EditProfilePage extends StatefulWidget {
  final ProfileUser user;

  const EditProfilePage({super.key, required this.user});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  // mobile Image picker
  PlatformFile? imagePickedFile;
  // web Image picker
  Uint8List? webImage;
  // temp wallet cubit
  late final walletCubit = context.read<WalletCubit>();

  final bioTextController = TextEditingController();
  late PostCategory selectedCategory;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    selectedCategory = widget.user.selectedPostCategory;
    // Set initial bio text if exists
    bioTextController.text = widget.user.bio;
  }

  @override
  void dispose() {
    bioTextController.dispose();
    super.dispose();
  }

  // Pick image method
  Future<void> pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        withData: kIsWeb, // Important for web
      );

      if (result != null) {
        setState(() {
          imagePickedFile = result.files.first;
          if (kIsWeb) {
            webImage = imagePickedFile!.bytes;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        toastification.show(
          alignment: Alignment.topCenter,
          title: Text('Image SelectionFailed'),
          description: Text('Failed to pick image: $e'),
          type: ToastificationType.warning,
          style: ToastificationStyle.flatColored,
          autoCloseDuration: Duration(seconds: 3),
        );
      }
    }
  }

  void updateProfile() async {
    if (_isUpdating) return; // Prevent multiple updates

    setState(() {
      _isUpdating = true;
    });

    try {
      final profileCubit = context.read<ProfileCubit>();

      // Prepare images
      final String uid = widget.user.uid;
      final imageMobilePath = (!kIsWeb && imagePickedFile != null)
          ? imagePickedFile!.path
          : null;
      final imageWebBytes = (kIsWeb && imagePickedFile != null)
          ? imagePickedFile!.bytes
          : null;

      // Prepare bio - only send if changed
      String? bioText;
      final newBio = bioTextController.text.trim();
      if (newBio != (widget.user.bio)) {
        bioText = newBio.isNotEmpty ? newBio : null;
      }

      // Check if category changed
      PostCategory? categoryToUpdate;
      if (selectedCategory != widget.user.selectedPostCategory) {
        categoryToUpdate = selectedCategory;
      }

      // Validation - check if there's anything to update
      if (imagePickedFile == null &&
          bioText == null &&
          categoryToUpdate == null) {
        toastification.show(
          alignment: Alignment.topCenter,
          title: Text('Nothing to Update'),
          description: Text('You didnt update anything'),
          type: ToastificationType.info,
          style: ToastificationStyle.flatColored,
          autoCloseDuration: Duration(seconds: 3),
        );
        setState(() {
          _isUpdating = false;
        });
        return;
      }

      // Update profile
      await profileCubit.updateProfile(
        uid: uid,
        newBio: bioText,
        imageMobilePath: imageMobilePath,
        imageWebBytes: imageWebBytes,
        newSelectedPostCategory: categoryToUpdate,
      );
    } catch (e) {
      if (mounted) {
        toastification.show(
          alignment: Alignment.topCenter,
          title: Text('Failed to update Profile'),
          description: Text('Failed : $e'),
          type: ToastificationType.error,
          style: ToastificationStyle.flatColored,
          autoCloseDuration: Duration(seconds: 3),
        );
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  // Get category color similar to sidebar
  Color _getCategoryColor(PostCategory category) {
    switch (category) {
      case PostCategory.politics:
        return Colors.blue;
      case PostCategory.sports:
        return Colors.green;
      case PostCategory.entertainment:
        return Colors.purple;
      case PostCategory.news:
        return Colors.orange;
      case PostCategory.sex:
        return Colors.pink;
      case PostCategory.religion:
        return const Color.fromARGB(255, 0, 0, 0);
    }
  }

  // Build category pills
  // Build category pills
  Widget _buildCategoryPills() {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: PostCategory.values.map((category) {
        final isSelected = selectedCategory == category;
        final categoryColor = _getCategoryColor(category);

        return GestureDetector(
          onTap: _isUpdating
              ? null
              : () {
                  setState(() {
                    selectedCategory = category;
                  });
                },
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 2),
            padding: const EdgeInsets.symmetric(
              vertical: 8,
              horizontal: 16, // Add this horizontal padding!
            ),
            decoration: BoxDecoration(
              color: isSelected ? categoryColor : categoryColor.withAlpha(30),
              border: Border.all(
                color: isSelected
                    ? Colors.transparent
                    : categoryColor.withAlpha(100),
                width: 0.8,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              category.name.replaceFirst(
                category.name[0],
                category.name[0].toUpperCase(),
              ),
              style: TextStyle(
                fontSize: 12, // Slightly increased font size
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfileCubit, ProfileStates>(
      builder: (context, state) {
        // profile loading
        if (state is ProfileLoading) {
          return ConstrainedScaffold(
            resizeToAvoidBottomInset: false,
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  LoadingIndicator(),
                  SizedBox(height: 16),
                  Text('Updating profile...'),
                ],
              ),
            ),
          );
        }

        // profile error
        if (state is ProfileError) {
          return ConstrainedScaffold(
            appBar: AppBar(
              title: const Text("Edit Profile"),
              centerTitle: true,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error: ${state.message}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        // profile loaded or initial state, show edit form
        return buildEditPage();
      },
      listener: (context, state) {
        if (state is ProfileLoaded) {
          // Show success message and navigate back
          if (mounted) {
            Navigator.pop(context);
          }
          toastification.show(
            alignment: Alignment.topCenter,
            title: Text('Success'),
            description: Text('Profile updated successfully'),
            type: ToastificationType.success,
            style: ToastificationStyle.flatColored,
            autoCloseDuration: Duration(seconds: 3),
          );
        } else if (state is ProfileError) {
          // Show error message
          toastification.show(
            alignment: Alignment.topCenter,
            title: Text('Update Failed'),
            description: Text('Update failed: ${state.message}'),
            type: ToastificationType.error,
            style: ToastificationStyle.flatColored,
            autoCloseDuration: Duration(seconds: 3),
          );
          if (mounted) {
            setState(() {
              _isUpdating = false;
            });
          }
        }
      },
    );
  }

  Widget buildEditPage() {
    return ConstrainedScaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: const Text("Edit Profile"),
        centerTitle: true,
        foregroundColor: Theme.of(context).colorScheme.primary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile picture
            Center(
              child: Container(
                height: 200,
                width: 200,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary,
                  shape: BoxShape.circle,
                ),
                clipBehavior: Clip.hardEdge,
                child: _buildProfileImage(),
              ),
            ),
            const SizedBox(height: 25),

            // Pick image button
            Center(
              child: MaterialButton(
                onPressed: _isUpdating ? null : pickImage,
                color: Theme.of(context).colorScheme.tertiary,
                child: const Text('Pick Image'),
              ),
            ),

            const SizedBox(height: 25),

            // Bio section
            const Text(
              "Bio",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            MyTextField(
              controller: bioTextController,
              hintText: 'Enter your bio...',
              obscureText: false,
              maxLines: 3,
            ),

            // Category selection
            const SizedBox(height: 25),
            const Text(
              "Your Post Category",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 15),
            _buildCategoryPills(),

            const SizedBox(height: 40),

            // Save Profile Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isUpdating ? null : updateProfile,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isUpdating
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.tertiary,
                  foregroundColor: Theme.of(context).colorScheme.surface,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: _isUpdating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: LoadingIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Text(
                        'Save Profile',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    // Display selected image for mobile
    if (!kIsWeb && imagePickedFile != null) {
      return Image.file(File(imagePickedFile!.path!), fit: BoxFit.cover);
    }

    // Display selected image for web
    if (kIsWeb && webImage != null) {
      return Image.memory(webImage!, fit: BoxFit.cover);
    }

    // Display existing profile pic
    if (widget.user.profileImageUrl.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: widget.user.profileImageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => const LoadingIndicator(),
        errorWidget: (context, url, error) => Icon(
          Icons.person,
          size: 72,
          color: Theme.of(context).colorScheme.primary,
        ),
      );
    }

    // Default avatar
    return Icon(
      Icons.person,
      size: 72,
      color: Theme.of(context).colorScheme.primary,
    );
  }
}
