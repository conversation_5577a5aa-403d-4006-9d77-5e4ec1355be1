import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/post/domain/entities/comment.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/post/domain/repos/post_repo.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_states.dart';
import 'package:moneymouthy/features/storage/domain/storage_repo.dart';
import 'package:moneymouthy/features/wallet/domain/repos/wallet_repo.dart';
import 'package:moneymouthy/features/poll/domain/entities/poll.dart'
    as poll_entities;
import 'package:moneymouthy/features/poll/domain/repos/poll_repo.dart';

class PostCubit extends Cubit<PostStates> {
  final PostRepo postRepo;
  final StorageRepo storageRepo;
  final WalletRepo walletRepo;
  final PollRepo pollRepo;
  final Set<String> _ongoingPostCreations = <String>{};

  PostCubit({
    required this.postRepo,
    required this.storageRepo,
    required this.walletRepo,
    required this.pollRepo,
  }) : super(PostsInitial());

  // Create a new post with multiple images support
  Future<void> createPost(
    Post post, {
    List<String>? imagePaths,
    String? videoPath,
    List<Uint8List>? imageBytes,
    Uint8List? videoBytes,
    String? pollQuestion,
  }) async {
    if (_ongoingPostCreations.contains(post.id)) {
      emit(PostsError("Post creation already in progress"));
      return;
    }

    _ongoingPostCreations.add(post.id);

    try {
      emit(PostsUploadingProgress("Preparing upload...", 0.1));

      // Upload media
      final (imageUrls, videoUrl) = await _uploadMedia(
        post.id,
        imagePaths,
        videoPath,
        imageBytes,
        videoBytes,
      );

      // Create poll if needed
      final pollId = await _createPoll(pollQuestion, post);

      // Create updated post
      final updatedPost = post.copyWith(
        imageUrls: imageUrls.isNotEmpty ? imageUrls : post.imageUrls,
        videoUrl: videoUrl ?? post.videoUrl,
        pollId: pollId,
      );

      // Create post and process payment
      await _createPostAndProcessPayment(updatedPost);

      // Finalize
      emit(PostsUploadingProgress("Finalizing...", 1.0));
      await _safeRefreshPosts();
    } catch (e) {
      emit(PostsError(_getDetailedErrorMessage(e)));
    } finally {
      _ongoingPostCreations.remove(post.id);
    }
  }

  // Legacy method for backward compatibility
  Future<void> createPostLegacy(
    Post post, {
    String? imagePath,
    String? videoPath,
    Uint8List? imageBytes,
    Uint8List? videoBytes,
  }) async {
    return createPost(
      post,
      imagePaths: imagePath != null ? [imagePath] : null,
      videoPath: videoPath,
      imageBytes: imageBytes != null ? [imageBytes] : null,
      videoBytes: videoBytes,
    );
  }

  Future<void> fetchAllPosts() async {
    try {
      emit(PostsLoading());
      final posts = await postRepo.fetchAllPosts();
      emit(PostsLoaded(posts));
    } catch (e) {
      emit(PostsError("Failed to fetch posts: $e"));
    }
  }

  Future<void> fetchPostsByCategory(PostCategory category) async {
    try {
      emit(PostsLoading());
      final posts = await postRepo.fetchPostsByCategory(category);
      emit(PostsLoaded(posts));
    } catch (e) {
      emit(PostsError("Failed to fetch posts by category: $e"));
    }
  }

  Future<Post> getPostById(String postId) async {
    try {
      final post = await postRepo.getPostById(postId);
      return post ?? Post.empty();
    } catch (e) {
      return Post.empty();
    }
  }

  Future<void> deletePost(String postId) async {
    try {
      if (state is! PostsLoaded) return;

      final currentPosts = (state as PostsLoaded).posts;
      final updatedPosts = currentPosts
          .where((post) => post.id != postId)
          .toList();

      emit(PostsLoaded(updatedPosts));
      await postRepo.deletePost(postId);
    } catch (e) {
      await fetchAllPosts();
      emit(PostsError("Failed to delete post: $e"));
    }
  }

  Future<void> toggleLikePost(String postId, String userId) async {
    await _togglePostInteraction(postId, userId, true);
  }

  Future<void> toggleHatePost(String postId, String userId) async {
    await _togglePostInteraction(postId, userId, false);
  }

  Future<void> addComment(String postId, Comment comment) async {
    try {
      await postRepo.addComment(postId, comment);
    } catch (e) {
      emit(PostsError("Failed to add comment: $e"));
    }
  }

  Future<void> deleteComment(String postId, String commentId) async {
    try {
      if (state is! PostsLoaded) return;

      final currentPosts = (state as PostsLoaded).posts;
      final updatedPosts = currentPosts.map((post) {
        if (post.id == postId) {
          final updatedComments = post.comments
              .where((comment) => comment.id != commentId)
              .toList();
          return post.copyWith(comments: updatedComments);
        }
        return post;
      }).toList();

      emit(PostsLoaded(updatedPosts));
      await postRepo.deleteComment(postId, commentId);
    } catch (e) {
      await fetchAllPosts();
      emit(PostsError("Failed to delete comment: $e"));
    }
  }

  // Private helper methods
  Future<(List<String>, String?)> _uploadMedia(
    String postId,
    List<String>? imagePaths,
    String? videoPath,
    List<Uint8List>? imageBytes,
    Uint8List? videoBytes,
  ) async {
    List<String> imageUrls = [];
    String? videoUrl;

    // Upload images
    final hasImages = kIsWeb
        ? (imageBytes?.isNotEmpty ?? false)
        : (imagePaths?.isNotEmpty ?? false) ||
              (imageBytes?.isNotEmpty ?? false);

    debugPrint('=== IMAGE UPLOAD DEBUG ===');
    debugPrint('kIsWeb: $kIsWeb');
    debugPrint('hasImages: $hasImages');
    debugPrint('imageBytes count: ${imageBytes?.length ?? 0}');
    debugPrint('imagePaths count: ${imagePaths?.length ?? 0}');

    if (hasImages) {
      emit(PostsUploadingProgress("Uploading images...", 0.3));
      List<String> pathUrls = [];
      List<String> byteUrls = [];

      if (kIsWeb) {
        debugPrint('Starting web image upload...');
        imageUrls = await storageRepo.uploadMultiplePostImagesWeb(
          imageBytes!,
          postId,
        );
        debugPrint('Web upload completed. URLs: ${imageUrls.length}');
      } else {
        // Mobile can have both path-based and byte-based images
        if (imagePaths?.isNotEmpty ?? false) {
          debugPrint('Starting mobile path-based image upload...');
          pathUrls = await storageRepo.uploadMultiplePostImagesMobile(
            imagePaths!,
            postId,
          );
          debugPrint('Mobile path upload completed. URLs: ${pathUrls.length}');
        }

        if (imageBytes?.isNotEmpty ?? false) {
          debugPrint('Starting mobile byte-based image upload...');
          byteUrls = await storageRepo.uploadMultiplePostImagesWeb(
            imageBytes!,
            postId,
          );
          debugPrint('Mobile byte upload completed. URLs: ${byteUrls.length}');
        }

        // Combine both types of uploads
        imageUrls = [...pathUrls, ...byteUrls];
        debugPrint('Total mobile upload completed. URLs: ${imageUrls.length}');
      }
    }

    // Upload video
    if (videoPath != null) {
      emit(PostsUploadingProgress("Uploading video...", 0.5));
      if (kIsWeb) {
        videoUrl = await storageRepo.uploadPostVideoWeb(videoBytes!, postId);
      } else {
        videoUrl = await storageRepo.uploadPostVideoMobile(videoPath, postId);
      }
    }

    return (imageUrls, videoUrl);
  }

  Future<String?> _createPoll(String? pollQuestion, Post post) async {
    if (pollQuestion?.isNotEmpty ?? false) {
      final poll = poll_entities.Poll(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        question: pollQuestion!,
        postId: post.id,
        creatorId: post.userId,
        createdAt: DateTime.now(),
        votes: [],
        allowMultipleVotes: false,
      );
      await pollRepo.createPoll(poll);
      return poll.id;
    }
    return null;
  }

  Future<void> _createPostAndProcessPayment(Post post) async {
    await postRepo.createPost(post);

    try {
      await walletRepo.deductBalance(
        post.userId,
        post.postCost,
        'Post creation: ${_truncateText(post.text)}...',
        'Post Creation',
      );
    } catch (paymentError) {
      try {
        await postRepo.deletePost(post.id);
      } catch (deleteError) {
        debugPrint('Failed to rollback post creation: $deleteError');
      }
      throw Exception('Payment failed: $paymentError');
    }
  }

  Future<void> _togglePostInteraction(
    String postId,
    String userId,
    bool isLike,
  ) async {
    try {
      final post = await postRepo.getPostById(postId);
      if (post == null) throw Exception("Post not found");

      final interactions = isLike ? post.likes : post.hates;
      final wasInteracted = interactions.contains(userId);
      final interactionType = isLike ? 'Like' : 'Hate';
      final cost = 0.05;

      // Toggle interaction in backend
      if (isLike) {
        await postRepo.toggleLikePost(postId, userId);
      } else {
        await postRepo.toggleHatePost(postId, userId);
      }

      // Handle wallet transaction
      final description =
          '$interactionType post: ${_truncateText(post.text)}...';

      if (!wasInteracted) {
        await walletRepo.deductBalance(
          userId,
          cost,
          description,
          '$interactionType Interaction',
        );
      } else {
        await walletRepo.addBalance(
          userId,
          cost,
          'Un$description',
          methodType: '$interactionType Refund',
        );
      }
    } catch (e) {
      emit(
        PostsError(
          "Failed to ${isLike ? 'like' : 'hate'}/un${isLike ? 'like' : 'hate'} post: $e",
        ),
      );
    }
  }

  Future<void> _safeRefreshPosts() async {
    try {
      await fetchAllPosts();
    } catch (fetchError) {
      debugPrint('Failed to refetch posts after creation: $fetchError');
      emit(PostsLoaded([]));
    }
  }

  String _truncateText(String text, [int maxLength = 20]) {
    return text.length > maxLength ? text.substring(0, maxLength) : text;
  }

  String _getDetailedErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('image upload failed')) {
      return 'Failed to upload images. Please check your internet connection and try again.';
    } else if (errorString.contains('video upload failed')) {
      return 'Failed to upload video. Please check your internet connection and try again.';
    } else if (errorString.contains('payment failed')) {
      return 'Payment processing failed. Your balance has not been deducted. Please try again.';
    } else if (errorString.contains('poll')) {
      return 'Failed to create poll. Please try again.';
    } else if (errorString.contains('network') ||
        errorString.contains('connection')) {
      return 'Network error. Please check your internet connection and try again.';
    } else if (errorString.contains('permission')) {
      return 'Permission denied. Please check your account permissions.';
    } else if (errorString.contains('storage')) {
      return 'Storage error. Please try again or contact support.';
    } else if (errorString.contains('already in progress')) {
      return 'Upload already in progress. Please wait for it to complete.';
    } else {
      return 'Failed to create PutUp. Please try again.';
    }
  }
}
