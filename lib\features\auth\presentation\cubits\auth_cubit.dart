/*

Auth Cubit : State Management for Auth Feature

*/

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';
import 'package:moneymouthy/features/auth/domain/repos/auth_repo.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';
import 'package:moneymouthy/features/profile/domain/repos/profile_repo.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepo authRepo;
  final ProfileRepo profileRepo;
  AppUser? _currentUser;
  AuthCubit({required this.authRepo, required this.profileRepo})
    : super(AuthInitial());

  // check if user already authenticated
  void checkAuth() async {
    final AppUser? user = await authRepo.getCurrentUser();
    if (user != null) {
      _currentUser = user;
      emit(Authenticated(user));
    } else {
      emit(UnAuthenticated());
    }
  }

  // get current user
  AppUser? get currentUser => _currentUser;
  // login with email + pass
  Future<void> login(String email, String password) async {
    try {
      emit(AuthLoading());
      final AppUser? user = await authRepo.loginWithEmailAndPassword(
        email,
        password,
      );
      if (user != null) {
        _currentUser = user;
        emit(Authenticated(user));
      } else {
        emit(UnAuthenticated());
      }
    } catch (e) {
      emit(AuthError(e.toString()));
      emit(UnAuthenticated());
    }
  }

  // register with email + pass
  Future<void> register(String email, String password, String name) async {
    try {
      emit(AuthLoading());
      final AppUser? user = await authRepo.registerWithEmailAndPassword(
        email,
        password,
        name,
      );
      if (user != null) {
        _currentUser = user;
        emit(Authenticated(user));
        // let the user follow all other users
        await profileRepo.makeUserFollowAllRemainingOptimized(user.uid);
      } else {
        emit(UnAuthenticated());
      }
    } catch (e) {
      emit(AuthError(e.toString()));
      emit(UnAuthenticated());
    }
  }

  Future<void> logout() async {
    try {
      emit(AuthLoading());
      await authRepo.logout();
      _currentUser = null;
      emit(UnAuthenticated());
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  // logout
}
