import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:moneymouthy/globals.dart';
import 'package:toastification/toastification.dart';

class EnhancedShareWidget extends StatelessWidget {
  final String postId;
  final String postText;

  const EnhancedShareWidget({
    super.key,
    required this.postId,
    required this.postText,
  });

  String get shareUrl => '${appUrl()}/#/post/$postId';

  String get shareText =>
      'Check out this post: ${postText.length > 50 ? '${postText.substring(0, 50)}...' : postText}';

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Share this PutUp',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),

          // Copy Link Option
          _buildShareOption(
            context,
            icon: Icons.copy,
            title: 'Copy Link',
            onTap: () => _copyLink(context),
          ),

          const SizedBox(height: 10),

          // Social Media Grid
          Text(
            'Share on Social Media',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 6,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            children: [
              _buildSocialMediaButton(
                context,
                'Facebook',
                'facebook',
                () => _shareToFacebook(context),
                color: Color(0xFF0866FF),
              ),
              _buildSocialMediaButton(
                context,
                'X',
                'x',
                () => _shareToX(context),
                color: Theme.of(context).colorScheme.secondary,
              ),
              _buildSocialMediaButton(
                context,
                'Reddit',
                'reddit',
                () => _shareToReddit(context),
                color: Color(0xFFFF4500),
              ),
              _buildSocialMediaButton(
                context,
                'TikTok',
                'tiktok',
                () => _shareGeneric(context),
                color: Theme.of(context).colorScheme.secondary,
              ),
              _buildSocialMediaButton(
                context,
                'Instagram',
                'instagram',
                () => _shareGeneric(context),
                color: Color(0xFFFF0069),
              ),
              _buildSocialMediaButton(
                context,
                'Medium',
                'medium',
                () => _shareGeneric(context),
                color: Theme.of(context).colorScheme.secondary,
              ),
              _buildSocialMediaButton(
                context,
                'Messenger',
                'messenger',
                () => _shareToMessenger(context),
                color: Color(0xFF0866FF),
              ),
              _buildSocialMediaButton(
                context,
                'Gmail',
                'gmail',
                () => _shareToGmail(context),
                color: Color(0xFFEA4335),
              ),
              _buildSocialMediaButton(
                context,
                'Messages',
                'googlemessages',
                () => _shareToMessages(context),
                color: Color(0xFF1A73E8),
              ),
              _buildSocialMediaButton(
                context,
                'iMessage',
                'imessage',
                () => _shareToMessages(context),
                color: Color(0xFF34DA50),
              ),
              _buildSocialMediaButton(
                context,
                'Substack',
                'substack',
                () => _shareGeneric(context),
                color: Color(0xFFFF6719),
              ),
              _buildSocialMediaButton(
                context,
                'BlueSky',
                'bluesky',
                () => _shareToBluesky(context),
                color: Color(0xFF0285FF),
              ),
              _buildSocialMediaButton(
                context,
                'Fanbase',
                'fanbase',
                () => _shareToFanbase(context),
                color: Theme.of(context).colorScheme.secondary,
              ),
              _buildSocialMediaButton(
                context,
                'Threads',
                'threads',
                () => _shareToThreads(context),
                color: Theme.of(context).colorScheme.secondary,
              ),
              _buildSocialMediaButton(
                context,
                'Truth Social',
                'truthsocial',
                () => _shareToTruthSocial(context),
              ),
              _buildSocialMediaButton(
                context,
                'More',
                'share',
                () => _shareGeneric(context),
                color: Color(0xFF6e6e6e),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// UI Helpers
  Widget _buildShareOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 12),
            Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialMediaButton(
    BuildContext context,
    String name,
    String assetName,
    VoidCallback onTap, {
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 35,
            height: 35,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Color.fromARGB(62, 146, 145, 145),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: SvgPicture.asset(
              'assets/icons/$assetName.svg',
              fit: BoxFit.contain,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            name,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(fontSize: 10),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// Share Functions

  void _copyLink(BuildContext context) {
    Clipboard.setData(ClipboardData(text: shareUrl));
    Navigator.pop(context);
    _showToast('Link copied to clipboard!');
  }

  void _shareToFacebook(BuildContext context) {
    final url =
        'https://www.facebook.com/sharer/sharer.php?u=${Uri.encodeComponent(shareUrl)}';
    _launchUrl(context, url);
  }

  void _shareToX(BuildContext context) {
    final url =
        'https://x.com/intent/tweet?text=${Uri.encodeComponent('$shareText\n$shareUrl')}';
    _launchUrl(context, url);
  }

  void _shareToReddit(BuildContext context) {
    final url =
        'https://www.reddit.com/submit?url=${Uri.encodeComponent(shareUrl)}&title=${Uri.encodeComponent(shareText)}';
    _launchUrl(context, url);
  }

  void _shareToMessenger(BuildContext context) {
    final url =
        'fb-messenger://share/?link=${Uri.encodeComponent(shareUrl)}&app_id=123456789';
    _launchUrl(context, url);
  }

  void _shareToBluesky(BuildContext context) {
    final url =
        'https://bsky.app/intent/compose?text=${Uri.encodeComponent('$shareText\n$shareUrl')}';
    _launchUrl(context, url);
  }

  void _shareToThreads(BuildContext context) {
    final url =
        'https://threads.net/intent/post?text=${Uri.encodeComponent('$shareText\n$shareUrl')}';
    _launchUrl(context, url);
  }

  void _shareToGmail(BuildContext context) {
    final subject = Uri.encodeComponent('Check out this post');
    final body = Uri.encodeComponent('$shareText\n\n$shareUrl');
    final url = 'mailto:?subject=$subject&body=$body';
    _launchUrl(context, url);
  }

  void _shareToMessages(BuildContext context) {
    final text = Uri.encodeComponent('$shareText\n$shareUrl');
    final url = 'sms:?body=$text';
    _launchUrl(context, url);
  }

  void _shareToTruthSocial(BuildContext context) {
    final url =
        'https://truthsocial.com/share?text=${Uri.encodeComponent('$shareText\n$shareUrl')}';
    _launchUrl(context, url);
  }

  void _shareToFanbase(BuildContext context) {
    // No official share intent, fallback
    _shareGeneric(context);
  }

  void _shareGeneric(BuildContext context) {
    Share.share('$shareText\n$shareUrl');
    Navigator.pop(context);
  }

  void _launchUrl(BuildContext context, String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        Navigator.pop(context);
      } else {
        _shareGeneric(context);
      }
    } catch (e) {
      _shareGeneric(context);
    }
  }

  void _showToast(String message) {
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text(message),
      type: ToastificationType.success,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: const Duration(seconds: 2),
    );
  }
}
