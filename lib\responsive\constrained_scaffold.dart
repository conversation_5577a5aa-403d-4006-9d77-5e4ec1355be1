/*
* Constrained Scaffold
* This widget is used to display the scaffold with a constrained width
* */
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class ConstrainedScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;

  final Widget? bottomNavigationBar;
  final bool? resizeToAvoidBottomInset;
  const ConstrainedScaffold({
    super.key,
    required this.body,
    this.appBar,
    this.bottomNavigationBar,
    this.resizeToAvoidBottomInset,
  });
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      width: double.infinity,
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 700),
          child: Container(
            decoration: BoxDecoration(
              border: Border.symmetric(
                vertical: BorderSide(
                  color: Theme.of(context).colorScheme.tertiary.withAlpha(150),
                  width: kIsWeb ? 1 : 0,
                ),
              ),
            ),
            child: <PERSON><PERSON><PERSON>(
              child: Scaffold(
                appBar: appBar,
                resizeToAvoidBottomInset: resizeToAvoidBottomInset,
                bottomNavigationBar: bottomNavigationBar,
                body: body,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
