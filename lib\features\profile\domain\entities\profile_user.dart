import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';

class ProfileUser extends AppUser {
  final String bio;
  final String profileImageUrl;
  final List<String> following;
  final List<String> followers;
  final PostCategory selectedPostCategory;
  final double postAmount;
  final bool isBlocked;

  ProfileUser({
    required this.bio,
    required this.profileImageUrl,
    required super.uid,
    required super.name,
    required super.email,
    required this.following,
    required this.followers,
    required this.selectedPostCategory,
    required this.postAmount,
    this.isBlocked = false,
  });

  // Method to update profile user
  ProfileUser copyWith({
    String? newBio,
    String? newProfileImageUrl,
    List<String>? newFollowing,
    List<String>? newFollowers,
    PostCategory? newSelectedPostCategory,
    double? newPostAmount,
    bool? newIsBlocked,
  }) {
    return ProfileUser(
      uid: uid,
      name: name,
      email: email,
      bio: newBio ?? bio,
      profileImageUrl: newProfileImageUrl ?? profileImageUrl,
      following: newFollowing ?? following,
      followers: newFollowers ?? followers,
      selectedPostCategory: newSelectedPostCategory ?? selectedPostCategory,
      postAmount: newPostAmount ?? postAmount,
      isBlocked: newIsBlocked ?? isBlocked,
    );
  }

  // convert profile user ->  to Json
  @override
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'bio': bio,
      'profileImageUrl': profileImageUrl,
      'following': following,
      'followers': followers,
      'selectedPostCategory': selectedPostCategory.name,
      'postAmount': postAmount,
      'isBlocked': isBlocked,
    };
  }

  // Convert Json to -> profile user
  factory ProfileUser.fromJson(Map<String, dynamic> json) {
    return ProfileUser(
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      bio: json['bio'] ?? '',
      profileImageUrl: json['profileImageUrl'] ?? '',
      following: List<String>.from(json['following'] ?? []),
      followers: List<String>.from(json['followers'] ?? []),
      selectedPostCategory: PostCategory.values.firstWhere(
        (e) => e.name == json['selectedPostCategory'],
        orElse: () => PostCategory.politics,
      ),
      postAmount: (json['postAmount'] as num?)?.toDouble() ?? 0.05,
      isBlocked: json['isBlocked'] ?? false,
    );
  }
}
