import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:media_kit/media_kit.dart';
import 'package:moneymouthy/features/admin/data/firebase_admin_repo.dart';
import 'package:moneymouthy/features/admin/data/firebase_admin_stats_repo.dart';
import 'package:moneymouthy/features/admin/data/firebase_admin_user_management_repo.dart';
import 'package:moneymouthy/features/admin/data/firebase_admin_wallet_management_repo.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_stats_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_user_management_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_wallet_management_cubit.dart';
import 'package:moneymouthy/features/auth/data/firebase_auth_repo.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';
import 'package:moneymouthy/features/post/data/firebase_post_repo.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_cubit.dart';
import 'package:moneymouthy/features/profile/data/firebase_profile_repo.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/search/data/firebase_search_repo.dart';
import 'package:moneymouthy/features/search/presentation/cubits/search_cubit.dart';
import 'package:moneymouthy/features/storage/data/firebase_storage_repo.dart';
import 'package:moneymouthy/features/wallet/data/firebase_wallet_repo.dart';
import 'package:moneymouthy/features/wallet/data/stripe_payment_repo.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:moneymouthy/features/poll/data/firebase_poll_repo.dart';
import 'package:moneymouthy/features/poll/presentation/cubits/poll_cubit.dart';
import 'package:moneymouthy/routes/app_router.dart';
import 'package:moneymouthy/themes/theme_cubit.dart';
import 'package:toastification/toastification.dart';
import 'features/home/<USER>/cubits/category_cubit.dart';
import 'features/post/presentation/cubits/post_cost_cubit.dart';
import 'features/wallet/presentation/cubits/wallet_balance_cubit.dart';

/// APP- Root Level
/// ---------------------------------------
/// Repositories: for the database
///  - firebase
///
/// Bloc Providers: For State Management
///  - auth
///  - profile
///  - post
///  - search
///  - theme
///
///  Router handles Auth State automatically:
///    - unauthenticated -> auth page (login/register)
///    - authenticated -> home page

class MainApp extends StatelessWidget {
  // Repository instances
  final firebaseAuthRepo = FirebaseAuthRepo();
  final firebaseProfileRepo = FirebaseProfileRepo();
  final firebaseStorageRepo = FirebaseStorageRepo();
  final firebasePostRepo = FirebasePostRepo();
  final firebaseSearchRepo = FirebaseSearchRepo();
  final firebaseWalletRepo = FirebaseWalletRepo();
  final stripePaymentRepo = StripePaymentRepo();
  final firebasePollRepo = FirebasePollRepo();
  final firebaseAdminRepo = FirebaseAdminRepo();
  final firebaseAdminUserManagementRepo = FirebaseAdminUserManagementRepo();
  final firebaseAdminStatsRepo = FirebaseAdminStatsRepo();
  final firebaseAdminWalletManagementRepo = FirebaseAdminWalletManagementRepo();

  MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialized MediaKit for video playback
    MediaKit.ensureInitialized();
    return MultiBlocProvider(
      providers: [
        // Auth Cubit - Initialize first as other cubits depend on it
        BlocProvider<AuthCubit>(
          create: (context) => AuthCubit(
            authRepo: firebaseAuthRepo,
            profileRepo: firebaseProfileRepo,
          )..checkAuth(),
        ),

        // Profile Cubits
        BlocProvider<ProfileCubit>(
          create: (context) => ProfileCubit(
            profileRepo: firebaseProfileRepo,
            storageRepo: firebaseStorageRepo,
          ),
        ),
        BlocProvider<CurrentUserProfileCubit>(
          create: (context) => CurrentUserProfileCubit(
            profileRepo: firebaseProfileRepo,
            storageRepo: firebaseStorageRepo,
          ),
        ),

        // Post Cubit
        BlocProvider<PostCubit>(
          create: (context) => PostCubit(
            postRepo: firebasePostRepo,
            storageRepo: firebaseStorageRepo,
            walletRepo: firebaseWalletRepo,
            pollRepo: firebasePollRepo,
          ),
        ),

        // Search Cubit
        BlocProvider<SearchCubit>(
          create: (context) => SearchCubit(searchRepo: firebaseSearchRepo),
        ),

        // Wallet Cubit
        BlocProvider<WalletCubit>(
          create: (context) => WalletCubit(
            walletRepo: firebaseWalletRepo,
            paymentRepo: stripePaymentRepo,
          ),
        ),

        // Poll Cubit
        BlocProvider<PollCubit>(
          create: (context) => PollCubit(
            pollRepo: firebasePollRepo,
            walletRepo: firebaseWalletRepo,
          ),
        ),

        // Theme Cubit
        BlocProvider<ThemeCubit>(create: (context) => ThemeCubit()),

        // Category Cubit - depends on CurrentUserProfileCubit
        BlocProvider<CategoryCubit>(
          create: (context) => CategoryCubit(
            currentUserProfileCubit: context.read<CurrentUserProfileCubit>(),
          ),
        ),

        // Post Cost Cubit
        BlocProvider<PostCostCubit>(create: (context) => PostCostCubit()),

        // Wallet Balance Cubit - depends on WalletCubit
        BlocProvider<WalletBalanceCubit>(
          create: (context) =>
              WalletBalanceCubit(walletCubit: context.read<WalletCubit>()),
        ),

        // Admin Cubit
        BlocProvider<AdminCubit>(
          create: (context) => AdminCubit(adminRepo: firebaseAdminRepo),
        ),

        // Admin User Management Cubit
        BlocProvider<AdminUserManagementCubit>(
          create: (context) => AdminUserManagementCubit(
            userManagementRepo: firebaseAdminUserManagementRepo,
          ),
        ),

        // Admin Stats Cubit
        BlocProvider<AdminStatsCubit>(
          create: (context) =>
              AdminStatsCubit(statsRepo: firebaseAdminStatsRepo),
        ),

        // Admin Wallet Management Cubit
        BlocProvider<AdminWalletManagementCubit>(
          create: (context) => AdminWalletManagementCubit(
            walletManagementRepo: firebaseAdminWalletManagementRepo,
          ),
        ),
      ],

      child: BlocBuilder<ThemeCubit, ThemeData>(
        builder: (context, theme) {
          return BlocListener<AuthCubit, AuthState>(
            listener: (context, authState) {
              // Handle auth errors with toast notifications
              if (authState is AuthError) {
                toastification.show(
                  alignment: Alignment.topCenter,
                  title: const Text('Authentication Error'),
                  description: Text(authState.message),
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  autoCloseDuration: const Duration(seconds: 3),
                );
              }
            },
            child: ToastificationWrapper(
              child: MaterialApp.router(
                // title: 'Social App',
                routerConfig: AppRouter.router(context),
                debugShowCheckedModeBanner: false,
                theme: theme,
              ),
            ),
          );
        },
      ),
    );
  }
}
