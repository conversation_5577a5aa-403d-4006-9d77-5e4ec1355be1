import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/post/presentation/components/post_tile.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_cubit.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_states.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/post/presentation/pages/upload_post_page.dart';
import 'package:moneymouthy/features/profile/presentation/pages/profile_page.dart';
import 'package:moneymouthy/features/search/presentation/cubits/pages/search_page.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:moneymouthy/features/wallet/presentation/pages/wallet_page.dart';
import 'package:moneymouthy/features/settings/pages/settings_page.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';
import 'package:moneymouthy/features/home/<USER>/components/sidebar.dart';
import 'package:moneymouthy/features/home/<USER>/cubits/category_cubit.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => HomePageState();
}

class HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  int _currentTabIndex = 2; // Default to "All" tab (index 2)
  PostCategory? userSelectedCategory;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void switchTab(int index) {
    if (index >= 0 && index < 6 && index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      // load data when switching tabs
      if (index == 0) {
        loadData();
      }
    }
  }

  void _onCategoryChanged(PostCategory? category) {
    context.read<CategoryCubit>().selectCategory(category);
    setState(() {
      userSelectedCategory = category;
      _currentTabIndex = category != null ? 0 : 1;
    });
    // Refresh posts when category changes
    context.read<PostCubit>().fetchAllPosts();
  }

  // load data
  void loadData() {
    context.read<PostCubit>().fetchAllPosts();
    final user = context.read<AuthCubit>().currentUser;
    if (user != null) {
      // Load current user profile for sidebar
      context.read<CurrentUserProfileCubit>().fetchCurrentUserProfile(user.uid);
      context.read<WalletCubit>().loadWallet(user.uid);
    }

    // Initialize category from profile when available
    _initializeCategoryFromProfile();
  }

  void _initializeCategoryFromProfile() {
    final profileState = context.read<CurrentUserProfileCubit>().state;
    if (profileState is ProfileLoaded) {
      context.read<CategoryCubit>().initializeFromProfile(
        profileState.profileUser.selectedPostCategory,
      );
      setState(() {
        userSelectedCategory = profileState.profileUser.selectedPostCategory;
      });
    }
  }

  // Helper method to check if post is within 24h
  bool _isWithin24Hours(Post post) {
    final now = DateTime.now();
    final twentyFourHoursAgo = now.subtract(const Duration(hours: 24));
    return post.timestamp.isAfter(twentyFourHoursAgo);
  }

  // Get the top post (highest cost within 24h) for stickiness
  Post? _getTopStickyPost(List<Post> posts) {
    final recentPosts = posts.where(_isWithin24Hours).toList();
    if (recentPosts.isEmpty) return null;

    // Find the post with highest cost among recent posts
    recentPosts.sort((a, b) => b.postCost.compareTo(a.postCost));
    return recentPosts.first;
  }

  Widget _buildHomeFeed() {
    return BlocListener<CurrentUserProfileCubit, ProfileStates>(
      listener: (context, profileState) {
        if (profileState is ProfileLoaded) {
          // Update category cubit when profile changes
          context.read<CategoryCubit>().initializeFromProfile(
            profileState.profileUser.selectedPostCategory,
          );
          setState(() {
            userSelectedCategory =
                profileState.profileUser.selectedPostCategory;
          });
        }
      },
      child: BlocBuilder<CurrentUserProfileCubit, ProfileStates>(
        builder: (context, profileState) {
          if (profileState is ProfileLoaded) {
            userSelectedCategory =
                profileState.profileUser.selectedPostCategory;
          }

          return Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 6,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => setState(() => _currentTabIndex = 0),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: _currentTabIndex == 0
                                ? Theme.of(
                                    context,
                                  ).colorScheme.tertiary.withAlpha(60)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                              width: 0.3,
                            ),
                          ),
                          child: Text(
                            userSelectedCategory != null
                                ? userSelectedCategory!.name
                                      .replaceFirst(
                                        userSelectedCategory!.name[0],
                                        userSelectedCategory!.name[0]
                                            .toUpperCase(),
                                      )
                                      .toString()
                                : 'Category',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => setState(() => _currentTabIndex = 1),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: _currentTabIndex == 1
                                ? Theme.of(
                                    context,
                                  ).colorScheme.tertiary.withAlpha(60)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                              width: 0.3,
                            ),
                          ),
                          child: Text(
                            'Following',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => setState(() => _currentTabIndex = 2),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: _currentTabIndex == 2
                                ? Theme.of(
                                    context,
                                  ).colorScheme.tertiary.withAlpha(60)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                              width: 0.3,
                            ),
                          ),
                          child: Text(
                            'All',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(child: _buildPostsList()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPostsList() {
    return BlocBuilder<CategoryCubit, PostCategory?>(
      builder: (context, sidebarCategory) {
        return BlocBuilder<PostCubit, PostStates>(
          builder: (context, state) {
            if (state is PostsLoading || state is PostsUploading) {
              return Center(
                child: SpinKitDoubleBounce(
                  color: Theme.of(context).colorScheme.tertiary.withAlpha(200),
                ),
              );
            }

            if (state is PostsLoaded) {
              List<Post> postsToShow;
              bool showStickyPost = false;
              Post? stickyPost;

              // Determine which posts to show based on current tab and sidebar selection
              if (_currentTabIndex == 0) {
                // Category tab - use sidebar category or user's selected category
                PostCategory? filterCategory =
                    sidebarCategory ?? userSelectedCategory;

                if (filterCategory == null) {
                  return const Center(
                    child: Text('Select a category in the sidebar'),
                  );
                }

                final categoryPosts = state.posts
                    .where((post) => post.category == filterCategory)
                    .toList();

                if (categoryPosts.isEmpty) {
                  return Center(
                    child: Text('No ${filterCategory.name} posts found...'),
                  );
                }

                // Get sticky post for category tab only
                stickyPost = _getTopStickyPost(categoryPosts);
                showStickyPost = stickyPost != null;

                // Remove sticky post from regular list to avoid duplication
                postsToShow = categoryPosts
                    .where((post) => post.id != stickyPost?.id)
                    .toList();
              } else if (_currentTabIndex == 1) {
                // Following tab - show posts from users the current user is following
                return BlocBuilder<CurrentUserProfileCubit, ProfileStates>(
                  builder: (context, profileState) {
                    if (profileState is ProfileLoaded) {
                      final currentUser = profileState.profileUser;
                      final followingPosts = state.posts
                          .where(
                            (post) =>
                                currentUser.following.contains(post.userId),
                          )
                          .toList();

                      if (followingPosts.isEmpty) {
                        return const Center(
                          child: Text('No posts from people you follow...'),
                        );
                      }

                      return _buildPostsListView(followingPosts, null, false);
                    } else {
                      return const Center(child: LoadingIndicator());
                    }
                  },
                );
              } else {
                // All posts tab - show all posts, no sticky behavior
                postsToShow = state.posts;
                showStickyPost = false;

                if (postsToShow.isEmpty) {
                  return const Center(child: Text('No posts found...'));
                }
              }

              return _buildPostsListView(
                postsToShow,
                stickyPost,
                showStickyPost,
              );
            }

            if (state is PostsError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(state.message),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () =>
                          context.read<PostCubit>().fetchAllPosts(),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            return const SizedBox();
          },
        );
      },
    );
  }

  Widget _buildPostsListView(
    List<Post> posts,
    Post? stickyPost,
    bool showStickyPost,
  ) {
    if (showStickyPost && stickyPost != null) {
      // Build layout with sticky header and scrollable content
      return RefreshIndicator(
        onRefresh: () async {
          await context.read<PostCubit>().fetchAllPosts();
        },
        child: Column(
          children: [
            // Fixed sticky header that doesn't scroll
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4.0),
                    child: Row(
                      children: [
                        Icon(
                          Icons.push_pin,
                          color: Colors.amber.shade700,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Top',
                          style: TextStyle(
                            color: Colors.amber.shade700,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PostTile(
                    post: stickyPost,
                    onDeletePressed: () =>
                        context.read<PostCubit>().deletePost(stickyPost.id),
                  ),
                  const Divider(height: 1),
                ],
              ),
            ),

            // Scrollable content below the sticky header
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                cacheExtent: 200,
                itemCount: posts.length,
                itemBuilder: (context, index) {
                  final post = posts[index];
                  return PostTile(
                    post: post,
                    onDeletePressed: () =>
                        context.read<PostCubit>().deletePost(post.id),
                  );
                },
              ),
            ),
          ],
        ),
      );
    } else {
      // No sticky post - use regular ListView with RefreshIndicator
      return RefreshIndicator(
        onRefresh: () async {
          await context.read<PostCubit>().fetchAllPosts();
        },
        child: ListView.builder(
          controller: _scrollController,
          cacheExtent: 200,
          itemCount: posts.length,
          itemBuilder: (context, index) {
            final post = posts[index];
            return PostTile(
              post: post,
              onDeletePressed: () =>
                  context.read<PostCubit>().deletePost(post.id),
            );
          },
        ),
      );
    }
  }

  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeFeed(); // Home
      case 1:
        return const WalletPage(); // Wallet
      case 2:
        return const UploadPostPage(); // Create Post
      case 3:
        return const SearchPage(); // Search
      case 4:
        final user = context.read<AuthCubit>().currentUser;
        return ProfilePage(uid: user!.uid); // Profile
      case 5:
        return const SettingsPage(); // Settings
      default:
        return _buildHomeFeed();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      resizeToAvoidBottomInset: false,
      body: Row(
        children: [
          BlocBuilder<CategoryCubit, PostCategory?>(
            builder: (context, selectedCategory) {
              return Sidebar(
                selectedCategory: selectedCategory,
                onCategoryChanged: _onCategoryChanged,
              );
            },
          ),
          Expanded(
            child: Column(
              children: [
                Expanded(child: _buildCurrentPage()),
                // Bottom navigation bar moved to right column - hide when keyboard is open
                if (MediaQuery.of(context).viewInsets.bottom == 0)
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      boxShadow: [
                        BoxShadow(
                          blurRadius: 20,
                          color: Colors.black.withAlpha(25),
                        ),
                      ],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10.0,
                        vertical: 8,
                      ),
                      child: GNav(
                        tabBorderRadius: 20,
                        rippleColor: Theme.of(
                          context,
                        ).colorScheme.tertiary.withAlpha(25),
                        hoverColor: Theme.of(
                          context,
                        ).colorScheme.tertiary.withAlpha(13),
                        gap: 4,
                        activeColor: Theme.of(
                          context,
                        ).colorScheme.tertiary.withAlpha(200),
                        iconSize: 25,
                        textSize: 16,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 5,
                        ),
                        duration: const Duration(milliseconds: 400),
                        tabBackgroundColor: Theme.of(
                          context,
                        ).colorScheme.primary.withAlpha(25),
                        color: Theme.of(context).colorScheme.primary,
                        tabs: const [
                          GButton(icon: Icons.home, text: 'Home'),
                          GButton(
                            icon: Icons.account_balance_wallet,
                            text: 'Wallet',
                          ),
                          GButton(
                            icon: Icons.add_circle_outline,
                            text: 'PutUp',
                          ), // Create Post
                          GButton(icon: Icons.search, text: 'Search'),
                          GButton(icon: Icons.person, text: 'Profile'),
                          // GButton(icon: Icons.settings, text: 'Settings'),
                        ],
                        selectedIndex: _selectedIndex,
                        onTabChange:
                            switchTab, // Use the improved switchTab method
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
