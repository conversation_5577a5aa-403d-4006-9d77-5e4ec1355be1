import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_stats_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_stats_state.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';

class AdminHomePage extends StatefulWidget {
  const AdminHomePage({super.key});

  @override
  State<AdminHomePage> createState() => _AdminHomePageState();
}

class _AdminHomePageState extends State<AdminHomePage> {
  @override
  void initState() {
    super.initState();
    // Load statistics when page initializes
    context.read<AdminStatsCubit>().loadStats();
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<AdminStatsCubit>().refreshStats(),
          ),
        ],
      ),
      body: BlocBuilder<AdminStatsCubit, AdminStatsState>(
        builder: (context, state) {
          if (state is AdminStatsLoading) {
            return const Center(child: LoadingIndicator());
          }

          if (state is AdminStatsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.message,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () =>
                        context.read<AdminStatsCubit>().loadStats(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is AdminStatsLoaded) {
            return _buildDashboard(context, state.stats, state.topPosts);
          }

          return const Center(child: Text('Welcome to Admin Dashboard'));
        },
      ),
    );
  }

  Widget _buildDashboard(
    BuildContext context,
    Map<String, dynamic> stats,
    List<Map<String, dynamic>> topPosts,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overview Cards
          _buildOverviewSection(context, stats),

          const SizedBox(height: 24),

          // Recent Activity
          _buildRecentActivitySection(context, stats),

          const SizedBox(height: 24),

          // Posts by Category
          _buildCategorySection(context, stats),

          const SizedBox(height: 24),

          // Top Posts
          _buildTopPostsSection(context, topPosts),
        ],
      ),
    );
  }

  Widget _buildOverviewSection(
    BuildContext context,
    Map<String, dynamic> stats,
  ) {
    return Wrap(
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overview',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 5 : 3,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          children: [
            _buildStatCard(
              context,
              'Total Users',
              stats['totalUsers']?.toString() ?? '0',
              Icons.people,
              Colors.blue,
            ),
            _buildStatCard(
              context,
              'Total Posts',
              stats['totalPosts']?.toString() ?? '0',
              Icons.article,
              Colors.green,
            ),
            _buildStatCard(
              context,
              'Total Balance',
              '\$${(stats['totalBalance'] as double?)?.toStringAsFixed(2) ?? '0.00'}',
              Icons.account_balance_wallet,
              Colors.orange,
            ),
            _buildStatCard(
              context,
              'Transactions',
              stats['totalTransactions']?.toString() ?? '0',
              Icons.swap_horiz,
              Colors.purple,
            ),
            // Stripe balance
            _buildStatCard(
              context,
              'Stripe',
              stats['totalStripeBalance']?.toString() ?? '0',
              Icons.money,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentActivitySection(
    BuildContext context,
    Map<String, dynamic> stats,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity (Last 7 days)',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActivityCard(
                context,
                'New Users',
                stats['recentUsers']?.toString() ?? '0',
                Icons.person_add,
                Colors.blue.shade100,
                Colors.blue.shade800,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActivityCard(
                context,
                'New Posts',
                stats['recentPosts']?.toString() ?? '0',
                Icons.post_add,
                Colors.green.shade100,
                Colors.green.shade800,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildActivityCard(
          context,
          'Blocked Users',
          stats['blockedUsers']?.toString() ?? '0',
          Icons.block,
          Colors.red.shade100,
          Colors.red.shade800,
        ),
      ],
    );
  }

  Widget _buildCategorySection(
    BuildContext context,
    Map<String, dynamic> stats,
  ) {
    final postsByCategory =
        stats['postsByCategory'] as Map<String, dynamic>? ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Posts by Category',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: postsByCategory.entries.map<Widget>((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        entry.key.toString().capitalize(),
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      Text(
                        entry.value.toString(),
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTopPostsSection(
    BuildContext context,
    List<Map<String, dynamic>> topPosts,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Top Posts',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            Text('by Engagement'),
          ],
        ),
        const SizedBox(height: 16),
        ...topPosts.map((post) => _buildTopPostCard(context, post)),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 15,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color backgroundColor,
    Color iconColor,
  ) {
    return Card(
      color: backgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(icon, color: iconColor, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: iconColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: iconColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopPostCard(BuildContext context, Map<String, dynamic> post) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: Colors.amber.shade700, size: 20),
                const SizedBox(width: 8),
                Text(
                  'By ${post['userName']}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${post['engagement']} engagements',
                    style: TextStyle(
                      color: Colors.amber.shade800,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              post['text'] ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.thumb_up, size: 16, color: Colors.green),
                const SizedBox(width: 4),
                Text(
                  '${post['likes']}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(width: 16),
                Icon(Icons.thumb_down, size: 16, color: Colors.red),
                const SizedBox(width: 4),
                Text(
                  '${post['hates']}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
