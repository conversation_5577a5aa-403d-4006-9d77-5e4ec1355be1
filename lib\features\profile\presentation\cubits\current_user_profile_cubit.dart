import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:moneymouthy/features/profile/domain/repos/profile_repo.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';
import 'package:moneymouthy/features/storage/domain/storage_repo.dart';

/// Separate cubit for managing the current user's profile
/// This prevents the sidebar from being affected when viewing other users' profiles
class CurrentUserProfileCubit extends Cubit<ProfileStates> {
  final ProfileRepo profileRepo;
  final StorageRepo storageRepo;

  CurrentUserProfileCubit({
    required this.profileRepo,
    required this.storageRepo,
  }) : super(ProfileInitial());

  // fetch current user profile using repo
  Future<void> fetchCurrentUserProfile(String uid) async {
    try {
      emit(ProfileLoading());
      final user = await profileRepo.fetchUserProfile(uid);
      if (user != null) {
        emit(ProfileLoaded(user));
      } else {
        emit(ProfileError('User not found'));
      }
    } catch (e) {
      emit(ProfileError("Failed to fetch user: $e"));
    }
  }

  // get user profile on given id -> useful for loading many profiles
  Future<ProfileUser?> getCurrentUserProfile(String uid) async {
    try {
      final user = await profileRepo.fetchUserProfile(uid);
      return user;
    } catch (e) {
      return null;
    }
  }

  // update bio and profile picture
  Future<void> updateCurrentUserProfile({
    required String uid,
    String? newBio,
    Uint8List? imageWebBytes,
    String? imageMobilePath,
    PostCategory? newSelectedPostCategory,
    double? newPostAmount,
  }) async {
    // Only emit loading if we're doing image upload
    if (imageWebBytes != null || imageMobilePath != null) {
      emit(ProfileLoading());
    }

    try {
      // fetch current user
      final currentUser = await profileRepo.fetchUserProfile(uid);

      if (currentUser == null) {
        emit(ProfileError('Failed to fetch user for Profile update'));
        return;
      }

      // Optimistic update for immediate UI response (category and post amount)
      if (newSelectedPostCategory != null || newPostAmount != null) {
        final optimisticProfile = currentUser.copyWith(
          newSelectedPostCategory: newSelectedPostCategory,
          newPostAmount: newPostAmount,
        );
        emit(ProfileLoaded(optimisticProfile));
      }

      // profile picture update
      String? imageDownloadUrl;

      // ensure there is an image to upload
      if (imageWebBytes != null || imageMobilePath != null) {
        try {
          // for mobile
          if (imageMobilePath != null) {
            // upload
            imageDownloadUrl = await storageRepo.uploadProfileImageMobile(
              imageMobilePath,
              uid,
            );
          }
          // for web
          else if (imageWebBytes != null) {
            // upload
            imageDownloadUrl = await storageRepo.uploadProfileImageWeb(
              imageWebBytes,
              uid,
            );
          }

          if (imageDownloadUrl == null) {
            emit(ProfileError('Failed to upload image'));
            return;
          }
        } catch (e) {
          emit(ProfileError('Image upload failed: $e'));
          return;
        }
      }

      // Create updated profile with proper null handling
      final updatedProfile = currentUser.copyWith(
        newBio: newBio, // Allow null to keep existing bio
        newProfileImageUrl:
            imageDownloadUrl, // Allow null to keep existing image
        newSelectedPostCategory:
            newSelectedPostCategory, // Allow null to keep existing category
        newPostAmount: newPostAmount, // Allow null to keep existing post amount
      );

      // update in repo
      await profileRepo.updateProfile(updatedProfile);

      // Emit the updated profile directly instead of refetching
      emit(ProfileLoaded(updatedProfile));
    } catch (e) {
      emit(ProfileError('Error updating Profile: $e'));
      // Add debug logging
      if (kDebugMode) {
        debugPrint('CurrentUserProfileCubit updateProfile error: $e');
      }
    }
  }

  // toggle follow/unfollow
  Future<void> toggleFollow(String currentUid, String targetUid) async {
    try {
      // Get current state
      if (state is! ProfileLoaded) return;
      final currentProfile = (state as ProfileLoaded).profileUser;

      // Optimistically update the following list
      final isCurrentlyFollowing = currentProfile.following.contains(targetUid);
      final updatedFollowing = List<String>.from(currentProfile.following);

      if (isCurrentlyFollowing) {
        updatedFollowing.remove(targetUid);
      } else {
        updatedFollowing.add(targetUid);
      }

      // Create updated profile and emit immediately
      final updatedProfile = currentProfile.copyWith(
        newFollowing: updatedFollowing,
      );
      emit(ProfileLoaded(updatedProfile));

      // Update in repo
      await profileRepo.toggleFollow(currentUid, targetUid);
    } catch (e) {
      // On error, refresh the profile to get the correct state
      await fetchCurrentUserProfile(currentUid);
      emit(ProfileError('Failed to toggle follow: $e'));
    }
  }
}
