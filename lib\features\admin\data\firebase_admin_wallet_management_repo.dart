/*
Firebase Implementation of Admin Wallet Management Repository
*/

import 'package:cloud_firestore/cloud_firestore.dart' as firestore;
import 'package:moneymouthy/features/admin/domain/repos/admin_wallet_management_repo.dart';
import 'package:moneymouthy/features/wallet/domain/entities/wallet.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart'
    as wallet_entity;

class FirebaseAdminWalletManagementRepo implements AdminWalletManagementRepo {
  final firestore.FirebaseFirestore firebaseFirestore =
      firestore.FirebaseFirestore.instance;

  @override
  Future<List<Map<String, dynamic>>> getAllUserWallets() async {
    try {
      // Fetch wallets and users in parallel for better performance
      final walletsFuture = firebaseFirestore.collection('wallets_new').get();
      final usersFuture = firebaseFirestore.collection('users').get();

      // Wait for both requests to complete
      final results = await Future.wait([walletsFuture, usersFuture]);
      final walletsSnapshot = results[0] as firestore.QuerySnapshot;
      final usersSnapshot = results[1] as firestore.QuerySnapshot;

      // Create a map of users for quick lookup
      final usersMap = <String, Map<String, dynamic>>{};
      for (final userDoc in usersSnapshot.docs) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final convertedUserData = _convertTimestamps(userData);
        usersMap[userDoc.id] = {
          'name': convertedUserData['name'] ?? 'Unknown',
          'email': convertedUserData['email'] ?? '',
          'profileImageUrl': convertedUserData['profileImageUrl'] ?? '',
        };
      }

      // Match wallets with user data
      return walletsSnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        final userId = doc.id;
        final userInfo = usersMap[userId];

        return {
          'userId': userId,
          'balance': (data['balance'] as num?)?.toDouble() ?? 0.0,
          'name': userInfo?['name'] ?? 'Unknown User',
          'email': userInfo?['email'] ?? '',
          'profileImageUrl': userInfo?['profileImageUrl'] ?? '',
        };
      }).toList();
    } catch (e) {
      throw Exception('Failed to get all user wallets: $e');
    }
  }

  @override
  Future<Wallet?> getWalletByUserId(String userId) async {
    try {
      final doc = await firebaseFirestore
          .collection('wallets_new')
          .doc(userId)
          .get();

      if (!doc.exists) return null;

      final data = doc.data()!;
      return Wallet(
        userId: userId,
        balance: (data['balance'] as num?)?.toDouble() ?? 0.0,
      );
    } catch (e) {
      throw Exception('Failed to get wallet: $e');
    }
  }

  @override
  Future<void> addBalanceToUserWallet(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      // Get current wallet
      final walletDoc = await firebaseFirestore
          .collection('wallets_new')
          .doc(userId)
          .get();
      final currentBalance = walletDoc.exists
          ? (walletDoc.data()?['balance'] as num?)?.toDouble() ?? 0.0
          : 0.0;
      final newBalance = currentBalance + amount;

      // Update wallet balance
      await firebaseFirestore.collection('wallets_new').doc(userId).set({
        'userId': userId,
        'balance': newBalance,
        'updatedAt': firestore.Timestamp.now(),
      }, firestore.SetOptions(merge: true));

      // Create transaction record
      final transaction = wallet_entity.Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        amount: amount,
        type: wallet_entity.TransactionType.deposit,
        description: description,
        methodType: 'admin_credit',
        timestamp: DateTime.now(),
      );

      await firebaseFirestore
          .collection('transactions_new')
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception('Failed to add balance: $e');
    }
  }

  @override
  Future<void> deductBalanceFromUserWallet(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      // Get current wallet
      final walletDoc = await firebaseFirestore
          .collection('wallets_new')
          .doc(userId)
          .get();
      final currentBalance = walletDoc.exists
          ? (walletDoc.data()?['balance'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      if (currentBalance < amount) {
        throw Exception('Insufficient balance');
      }

      final newBalance = currentBalance - amount;

      // Update wallet balance
      await firebaseFirestore.collection('wallets_new').doc(userId).set({
        'userId': userId,
        'balance': newBalance,
        'updatedAt': firestore.Timestamp.now(),
      }, firestore.SetOptions(merge: true));

      // Create transaction record
      final transaction = wallet_entity.Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        amount: amount,
        type: wallet_entity.TransactionType.spent,
        description: description,
        methodType: 'admin_debit',
        timestamp: DateTime.now(),
      );

      await firebaseFirestore
          .collection('transactions_new')
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception('Failed to deduct balance: $e');
    }
  }

  @override
  Future<List<wallet_entity.Transaction>> getUserTransactionHistory(
    String userId,
  ) async {
    try {
      final querySnapshot = await firebaseFirestore
          .collection('transactions_new')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return wallet_entity.Transaction.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get transaction history: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAllTransactions() async {
    try {
      // Fetch transactions and users in parallel
      final transactionsFuture = firebaseFirestore
          .collection('transactions_new')
          .orderBy('timestamp', descending: true)
          .limit(100)
          .get();

      final usersFuture = firebaseFirestore.collection('users').get();

      // Wait for both requests to complete
      final results = await Future.wait([transactionsFuture, usersFuture]);
      final transactionsSnapshot = results[0] as firestore.QuerySnapshot;
      final usersSnapshot = results[1] as firestore.QuerySnapshot;

      // Create a map of users for quick lookup
      final usersMap = <String, Map<String, dynamic>>{};
      for (final userDoc in usersSnapshot.docs) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final convertedUserData = _convertTimestamps(userData);
        usersMap[userDoc.id] = {
          'name': convertedUserData['name'] ?? 'Unknown',
          'email': convertedUserData['email'] ?? '',
        };
      }

      return transactionsSnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;

        // Convert Firestore Timestamp to DateTime
        if (data['timestamp'] != null &&
            data['timestamp'] is firestore.Timestamp) {
          data['timestamp'] = (data['timestamp'] as firestore.Timestamp)
              .toDate();
        }

        // Add user details from the users map
        final userId = data['userId'] ?? '';
        final userInfo = usersMap[userId];
        data['userName'] = userInfo?['name'] ?? 'Unknown User';
        data['userEmail'] = userInfo?['email'] ?? '';

        return data;
      }).toList();
    } catch (e) {
      throw Exception('Failed to get all transactions: $e');
    }
  }

  int min(int a, int b) => a < b ? a : b;

  @override
  Future<Map<String, dynamic>> getWalletStatistics() async {
    try {
      final wallets = await getAllUserWallets();
      final transactions = await getAllTransactions();

      double totalBalance = 0.0;
      double averageBalance = 0.0;
      int zeroBalanceUsers = 0;
      int lowBalanceUsers = 0; // Less than $1

      for (final wallet in wallets) {
        totalBalance += wallet['balance'] ?? 0.0;
        if ((wallet['balance'] ?? 0.0) == 0.0) zeroBalanceUsers++;
        if ((wallet['balance'] ?? 0.0) < 1.0) lowBalanceUsers++;
      }

      if (wallets.isNotEmpty) {
        averageBalance = totalBalance / wallets.length;
      }

      return {
        'totalUsers': wallets.length,
        'totalBalance': totalBalance,
        'averageBalance': averageBalance,
        'zeroBalanceUsers': zeroBalanceUsers,
        'lowBalanceUsers': lowBalanceUsers,
        'totalTransactions': transactions.length,
      };
    } catch (e) {
      throw Exception('Failed to get wallet statistics: $e');
    }
  }

  @override
  Future<void> bulkAddBalanceToAllUsers(
    double amount,
    String description,
  ) async {
    try {
      final wallets = await getAllUserWallets();

      // Use batch write for efficiency
      final batch = firebaseFirestore.batch();

      for (final wallet in wallets) {
        final newBalance = (wallet['balance'] ?? 0.0) + amount;
        final walletRef = firebaseFirestore
            .collection('wallets_new')
            .doc(wallet['userId']);

        batch.set(walletRef, {
          'userId': wallet['userId'],
          'balance': newBalance,
          'updatedAt': firestore.Timestamp.now(),
        }, firestore.SetOptions(merge: true));

        // Create transaction record
        final transactionId =
            '${wallet['userId']}_${DateTime.now().millisecondsSinceEpoch}';
        final transactionRef = firebaseFirestore
            .collection('transactions_new')
            .doc(transactionId);

        final transaction = wallet_entity.Transaction(
          id: transactionId,
          userId: wallet['userId'],
          amount: amount,
          type: wallet_entity.TransactionType.deposit,
          description: description,
          methodType: 'bulk_admin_credit',
          timestamp: DateTime.now(),
        );

        batch.set(transactionRef, transaction.toJson());
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to bulk add balance: $e');
    }

    // // Helper function to convert Timestamps to DateTime in a map
    // Map<String, dynamic> _convertTimestamps(Map<String, dynamic> data) {
    //   final converted = Map<String, dynamic>.from(data);

    //   converted.forEach((key, value) {
    //     if (value is firestore.Timestamp) {
    //       converted[key] = value.toDate();
    //     }
    //   });

    //   return converted;
    // }
  }

  // Helper function to convert Timestamps to DateTime in a map
  Map<String, dynamic> _convertTimestamps(Map<String, dynamic> data) {
    final converted = Map<String, dynamic>.from(data);

    converted.forEach((key, value) {
      if (value is firestore.Timestamp) {
        converted[key] = value.toDate();
      }
    });

    return converted;
  }
}
