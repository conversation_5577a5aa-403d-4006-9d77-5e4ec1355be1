import 'package:cloud_firestore/cloud_firestore.dart';

enum PollOption { yes, no, dontCare }

extension PollOptionExtension on PollOption {
  String get displayName {
    switch (this) {
      case PollOption.yes:
        return 'Yes';
      case PollOption.no:
        return 'No';
      case PollOption.dontCare:
        return "Don't Care";
    }
  }

  String get value {
    switch (this) {
      case PollOption.yes:
        return 'yes';
      case PollOption.no:
        return 'no';
      case PollOption.dontCare:
        return 'dont_care';
    }
  }

  static PollOption fromValue(String value) {
    switch (value) {
      case 'yes':
        return PollOption.yes;
      case 'no':
        return PollOption.no;
      case 'dont_care':
        return PollOption.dontCare;
      default:
        return PollOption.dontCare;
    }
  }
}

class PollVote {
  final String userId;
  final PollOption option;
  final DateTime timestamp;

  PollVote({
    required this.userId,
    required this.option,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'option': option.value,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }

  factory PollVote.fromJson(Map<String, dynamic> json) {
    return PollVote(
      userId: json['userId'],
      option: PollOptionExtension.fromValue(json['option']),
      timestamp: (json['timestamp'] as Timestamp).toDate(),
    );
  }
}

class Poll {
  final String id;
  final String question;
  final String postId;
  final String creatorId;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final List<PollVote> votes;
  final bool allowMultipleVotes;

  Poll({
    required this.id,
    required this.question,
    required this.postId,
    required this.creatorId,
    required this.createdAt,
    this.expiresAt,
    required this.votes,
    this.allowMultipleVotes = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'postId': postId,
      'creatorId': creatorId,
      'createdAt': Timestamp.fromDate(createdAt),
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
      'votes': votes.map((vote) => vote.toJson()).toList(),
      'allowMultipleVotes': allowMultipleVotes,
    };
  }

  factory Poll.fromJson(Map<String, dynamic> json) {
    return Poll(
      id: json['id'],
      question: json['question'],
      postId: json['postId'],
      creatorId: json['creatorId'],
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      expiresAt: json['expiresAt'] != null 
          ? (json['expiresAt'] as Timestamp).toDate() 
          : null,
      votes: (json['votes'] as List<dynamic>? ?? [])
          .map((voteJson) => PollVote.fromJson(voteJson))
          .toList(),
      allowMultipleVotes: json['allowMultipleVotes'] ?? false,
    );
  }

  Poll copyWith({
    String? id,
    String? question,
    String? postId,
    String? creatorId,
    DateTime? createdAt,
    DateTime? expiresAt,
    List<PollVote>? votes,
    bool? allowMultipleVotes,
  }) {
    return Poll(
      id: id ?? this.id,
      question: question ?? this.question,
      postId: postId ?? this.postId,
      creatorId: creatorId ?? this.creatorId,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      votes: votes ?? this.votes,
      allowMultipleVotes: allowMultipleVotes ?? this.allowMultipleVotes,
    );
  }

  // Helper methods for poll statistics
  int get totalVotes => votes.length;

  int getVotesForOption(PollOption option) {
    return votes.where((vote) => vote.option == option).length;
  }

  double getPercentageForOption(PollOption option) {
    if (totalVotes == 0) return 0.0;
    return (getVotesForOption(option) / totalVotes) * 100;
  }

  bool hasUserVoted(String userId) {
    return votes.any((vote) => vote.userId == userId);
  }

  PollOption? getUserVote(String userId) {
    final userVote = votes.where((vote) => vote.userId == userId).firstOrNull;
    return userVote?.option;
  }

  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }

  Map<PollOption, int> get voteCounts {
    return {
      PollOption.yes: getVotesForOption(PollOption.yes),
      PollOption.no: getVotesForOption(PollOption.no),
      PollOption.dontCare: getVotesForOption(PollOption.dontCare),
    };
  }

  Map<PollOption, double> get votePercentages {
    return {
      PollOption.yes: getPercentageForOption(PollOption.yes),
      PollOption.no: getPercentageForOption(PollOption.no),
      PollOption.dontCare: getPercentageForOption(PollOption.dontCare),
    };
  }
}
