// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD_lcbaRg1tNGkh0Gjo4fzphQBVqqTJ0PM',
    appId: '1:717677160958:web:ffb6673a2853424da738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    authDomain: 'money-mouthy.firebaseapp.com',
    storageBucket: 'money-mouthy.firebasestorage.app',
    measurementId: 'G-HW3XNYV8RZ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC1ydzTXfkhy-dCry83jPtIqDbUrcWx2Ow',
    appId: '1:717677160958:android:34824fbd0ec2632ea738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    storageBucket: 'money-mouthy.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC7VzLvXOHHTKDK-wQiC40XtyvxM4x5BZ0',
    appId: '1:717677160958:ios:1011706ab5a90281a738af',
    messagingSenderId: '717677160958',
    projectId: 'money-mouthy',
    storageBucket: 'money-mouthy.firebasestorage.app',
    iosClientId: '717677160958-h1dl4q4rb5tf007392bt2voll439ifei.apps.googleusercontent.com',
    iosBundleId: 'com.example.socialAppBlocFlutter',
  );
}
