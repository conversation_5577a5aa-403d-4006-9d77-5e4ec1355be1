import 'package:shared_preferences/shared_preferences.dart';

class DraftManager {
  static const String _draftKey = 'post_draft';

  /// Save draft text locally
  static Future<void> saveDraft(String postText) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_draftKey, postText);
  }

  /// Retrieve draft text (returns empty string if none)
  static Future<String> getDraft() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_draftKey) ?? '';
  }

  /// Clear the saved draft
  static Future<void> clearDraft() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_draftKey);
  }
}
