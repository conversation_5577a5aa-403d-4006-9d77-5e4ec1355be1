import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/home/<USER>/cubits/category_cubit.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/post/presentation/components/image_search_bottom_sheet.dart';
import 'package:moneymouthy/features/post/presentation/components/media_pill_button.dart';
import 'package:moneymouthy/features/post/presentation/components/link_management_widget.dart';
import 'package:moneymouthy/features/poll/presentation/components/poll_creation_widget.dart';
import 'package:moneymouthy/features/post/services/draft_manager.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';
import 'package:moneymouthy/features/post/presentation/services/media_selection_service.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_cubit.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_states.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_cost_cubit.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_balance_cubit.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:moneymouthy/globals.dart';
import 'package:toastification/toastification.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';

import '../../../storage/services/image_optimization_service.dart';

class UploadPostPage extends StatefulWidget {
  const UploadPostPage({super.key});

  @override
  State<UploadPostPage> createState() => _UploadPostPageState();
}

class _UploadPostPageState extends State<UploadPostPage> {
  final _textController = TextEditingController();
  final _focusNode = FocusNode(); // Add this
  Timer? _draftTimer;
  AppUser? _currentUser;

  // Media state - now supports all types together
  List<MediaFile> _selectedImages = [];
  List<MediaFile> _selectedVideos = []; // Changed to support multiple videos
  List<MediaFile> _selectedVideoAds = []; // VideoAd videos (60 seconds)
  List<String> _links = [];
  String? _pollQuestion; // Poll question if user creates a poll
  late bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _currentUser = context.read<AuthCubit>().currentUser;

    _initializeCostFromProfile();

    // Load draft when page opens
    DraftManager.getDraft().then((draft) {
      setState(() {
        _textController.text = draft;
      });
    });
  }

  void _initializeCostFromProfile() {
    // First check if PostCostCubit already has a value from the sidebar
    final currentCost = context.read<PostCostCubit>().state;

    // If the current cost is not the default, keep it (user selected from sidebar)
    if (currentCost > 0.05) {
      return; // Keep the current cost from sidebar
    }

    // Otherwise, set cost from user's profile postAmount
    final profileCubit = context.read<CurrentUserProfileCubit>();
    final profileState = profileCubit.state;

    if (profileState is ProfileLoaded) {
      final userPostAmount = profileState.profileUser.postAmount;
      context.read<PostCostCubit>().setFixedCost(userPostAmount);
    } else {
      // Default cost if profile not loaded
      context.read<PostCostCubit>().setFixedCost(0.05);
    }
  }

  @override
  void dispose() {
    // Save draft automatically when leaving page
    DraftManager.saveDraft(_textController.text);
    _textController.dispose();
    _focusNode.dispose(); // Add this
    _draftTimer?.cancel();

    super.dispose();
  }

  void _debounceDraftSave(String text) {
    _draftTimer?.cancel();
    _draftTimer = Timer(const Duration(milliseconds: 500), () {
      DraftManager.saveDraft(text);
    });
  }

  // Pre-optimize images when selected for better upload performance
  Future<void> _preOptimizeImages(List<MediaFile> images) async {
    try {
      // Run optimization in background to avoid blocking UI
      await Future.delayed(
        Duration(milliseconds: 100),
      ); // Small delay to allow UI to update

      // Note: Actual pre-optimization would require modifying MediaFile to store optimized versions
      // For now, we'll rely on optimization during upload
      debugPrint('Pre-optimization setup complete for ${images.length} images');
    } catch (e) {
      debugPrint('Pre-optimization failed: $e');
      // Continue without pre-optimization
    }
  }

  // --------------------CLEAR DRAFT
  void _clearDraft() async {
    await DraftManager.clearDraft();
    setState(() => _textController.clear());
  }

  // ---------------- MEDIA SELECTION ----------------
  void _handleImagesSelected(List<MediaFile> images) {
    setState(() {
      _selectedImages.addAll(images);
    });
  }

  void _handleVideoSelected(MediaFile video) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _selectedVideos.add(video);
        });
      }
    });
  }

  void _handleVideoAdSelected(MediaFile video) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _selectedVideoAds.add(video);
        });
      }
    });
  }

  void _handlePollSelected() {
    if (_pollQuestion != null) {
      // If poll already exists, remove it
      setState(() {
        _pollQuestion = null;
      });
    } else {
      // Show poll creation dialog
      _showPollCreationDialog();
    }
  }

  void _showPollCreationDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.poll, color: Colors.purple.shade700, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'Create Poll',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.purple.shade700,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      style: IconButton.styleFrom(
                        backgroundColor: Theme.of(
                          context,
                        ).colorScheme.surfaceContainerHighest,
                      ),
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: EdgeInsets.fromLTRB(
                    16,
                    0,
                    16,
                    MediaQuery.of(context).viewInsets.bottom + 16,
                  ),
                  child: PollCreationWidget(
                    onPollCreated: (question) {
                      setState(() {
                        _pollQuestion = question;
                      });
                      Navigator.pop(context);
                      // Restore focus after poll creation
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          _focusNode.requestFocus();
                        }
                      });
                    },
                    onCancel: () {
                      Navigator.pop(context);
                      // Restore focus when cancelled
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          _focusNode.requestFocus();
                        }
                      });
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<String?> _createTempFileFromBytes(
    Uint8List bytes,
    String fileName,
  ) async {
    try {
      // Get temporary directory
      final tempDir = await getTemporaryDirectory();

      // Create unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = fileName.contains('.')
          ? fileName.split('.').last
          : 'jpg';
      final tempFileName = 'temp_image_${timestamp}.$extension';

      // Create file path
      final tempFile = File('${tempDir.path}/$tempFileName');

      // Write bytes to file
      await tempFile.writeAsBytes(bytes);

      debugPrint('Created temp file: ${tempFile.path}');
      return tempFile.path;
    } catch (e) {
      debugPrint('Error creating temp file: $e');
      return null;
    }
  }

  Widget _buildImagePreview(MediaFile image) {
    try {
      // Priority 1: If we have bytes (web or searched images), use MemoryImage
      if (image.bytes != null && image.bytes!.isNotEmpty) {
        return Image.memory(
          image.bytes!,
          width: 40,
          height: 40,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Error displaying image bytes: $error');
            return Container(
              width: 40,
              height: 40,
              color: Colors.grey.shade300,
              child: const Icon(Icons.error, size: 16),
            );
          },
        );
      }
      // Priority 2: If we have path and it's a network URL
      else if (image.path != null && image.path!.startsWith('http')) {
        return Image.network(
          image.path!,
          width: 40,
          height: 40,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Error displaying network image: $error');
            return Container(
              width: 40,
              height: 40,
              color: Colors.grey.shade300,
              child: const Icon(Icons.error, size: 16),
            );
          },
        );
      }
      // Priority 3: If we have path and we're not on web (local file)
      else if (!kIsWeb && image.path != null && image.path!.isNotEmpty) {
        return Image.file(
          File(image.path!),
          width: 40,
          height: 40,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Error displaying file image: $error');
            return Container(
              width: 40,
              height: 40,
              color: Colors.grey.shade300,
              child: const Icon(Icons.error, size: 16),
            );
          },
        );
      }
      // Fallback: No valid image data
      else {
        debugPrint('No valid image data found for: ${image.name}');
        debugPrint('  - bytes: ${image.bytes?.length ?? 'null'}');
        debugPrint('  - path: ${image.path ?? 'null'}');
        debugPrint('  - kIsWeb: $kIsWeb');

        return Container(
          width: 40,
          height: 40,
          color: Colors.grey.shade300,
          child: const Icon(Icons.image_not_supported, size: 16),
        );
      }
    } catch (e) {
      debugPrint('Exception in _buildImagePreview: $e');
      debugPrint('Image details:');
      debugPrint('  - name: ${image.name}');
      debugPrint('  - bytes: ${image.bytes?.length ?? 'null'}');
      debugPrint('  - path: ${image.path ?? 'null'}');
      debugPrint('  - type: ${image.type}');

      return Container(
        width: 40,
        height: 40,
        color: Colors.red.shade200,
        child: const Icon(Icons.error, size: 16, color: Colors.red),
      );
    }
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  void _removeVideoAd(int index) {
    setState(() {
      _selectedVideoAds.removeAt(index);
    });
  }

  void _updateLinks(List<String> links) {
    setState(() {
      _links = links;
    });
  }

  // ---------------- VALIDATION ----------------
  bool _isValidContent(String text) {
    final trimmed = text.trim();
    if (trimmed.isEmpty || trimmed.length < 3 || trimmed.length > 480) {
      return false;
    }
    final cleanText = trimmed.replaceAll(RegExp(r'\s+'), '');
    if (cleanText.length > 1) {
      final firstChar = cleanText[0];
      if (cleanText.split('').every((c) => c == firstChar)) return false;
    }
    return RegExp(r'[a-zA-Z0-9]').hasMatch(trimmed);
  }

  String? _getValidationError(String text) {
    final trimmed = text.trim();
    if (trimmed.isEmpty) return 'Please provide post content';
    if (trimmed.length < 3) return 'Post content must be at least 3 characters';
    if (trimmed.length > 480) return 'Post exceeds 480 characters limit';
    if (!_isValidContent(trimmed)) return 'Please provide meaningful content';
    return null;
  }

  // ---------------- POST UPLOAD ----------------
  Future<void> _uploadPost() async {
    // Prevent multiple simultaneous uploads
    if (_isUpdating) {
      return;
    }

    final textError = _getValidationError(_textController.text);
    if (textError != null) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Validation Failed'),
        description: Text(textError),
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
      return;
    }

    // Check if there's any content
    final hasContent =
        _textController.text.trim().isNotEmpty ||
        _selectedImages.isNotEmpty ||
        _selectedVideos.isNotEmpty ||
        _selectedVideoAds.isNotEmpty ||
        _links.isNotEmpty ||
        _pollQuestion != null;

    if (!hasContent) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('No Content Added'),
        description: Text('Please add some content to your PutUp'),
        type: ToastificationType.info,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
      return;
    }

    final postCost = context.read<PostCostCubit>().state;
    final balance = context.read<WalletBalanceCubit>().state;
    final category = context.read<CategoryCubit>().state;

    if (balance < postCost) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Insufficient balance'),
        description: Text('Please ReUp your balance to PutUp'),
        type: ToastificationType.warning,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
      return;
    }

    setState(() {
      _isUpdating = true;
    });

    // // Pre-optimize images before starting upload - NOT OPTIMIZING
    // if (_selectedImages.isNotEmpty && !kIsWeb) {
    //   try {
    //     toastification.show(
    //       alignment: Alignment.topCenter,
    //       title: Text('Preparing images...'),
    //       description: null,
    //       type: ToastificationType.info,
    //       style: ToastificationStyle.flatColored,
    //       autoCloseDuration: Duration(seconds: 2),
    //     );

    //     // Optimize images in background
    //     final paths = _selectedImages.map((img) => img.path!).toList();
    //     final optimizedFiles =
    //         await ImageOptimizationService.optimizeMultipleImageFiles(paths);

    //     // Update selected images with optimized paths
    //     if (optimizedFiles.isNotEmpty) {
    //       for (
    //         int i = 0;
    //         i < optimizedFiles.length && i < _selectedImages.length;
    //         i++
    //       ) {
    //         _selectedImages[i] = MediaFile(
    //           path: optimizedFiles[i].path,
    //           name: _selectedImages[i].name,
    //           type: MediaType.image,
    //         );
    //       }
    //     }
    //   } catch (e) {
    //     debugPrint(
    //       'Pre-optimization failed, continuing with original images: $e',
    //     );
    //   }
    // }

    context.read<WalletBalanceCubit>().deductOptimistically(postCost);

    final newPost = Post(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: _currentUser!.uid,
      userName: _currentUser!.name,
      text: _textController.text.trim(),
      imageUrls: [],
      videoUrl: '',
      links: _links,
      timestamp: DateTime.now(),
      likes: [],
      hates: [],
      comments: [],
      category: category ?? PostCategory.politics,
      postCost: postCost,
    );

    final postCubit = context.read<PostCubit>();

    // Prepare media for upload
    List<String>? imagePaths;
    List<Uint8List>? imageBytes;
    String? videoPath;
    Uint8List? videoBytes;

    if (_selectedImages.isNotEmpty) {
      if (kIsWeb) {
        // For web, we need bytes - but be more lenient with validation
        final validImages = <MediaFile>[];

        for (final img in _selectedImages) {
          if (img.bytes != null && img.bytes!.isNotEmpty) {
            // Basic validation - just check if we have data
            if (img.bytes!.length > 50) {
              // Very minimal check
              validImages.add(img);
            } else {
              debugPrint('Skipping image with insufficient data: ${img.name}');
            }
          } else {
            debugPrint('Skipping image with null/empty bytes: ${img.name}');
          }
        }

        if (validImages.isEmpty) {
          toastification.show(
            alignment: Alignment.topCenter,
            title: Text('No Valid Images'),
            description: Text(
              'No valid images found. Please try selecting images again.',
            ),
            type: ToastificationType.error,
            style: ToastificationStyle.flatColored,
            autoCloseDuration: Duration(seconds: 3),
          );
          setState(() {
            _isUpdating = false;
          });
          return;
        }

        imageBytes = validImages.map((img) => img.bytes!).toList();

        // Update selected images list if some were filtered out
        if (validImages.length != _selectedImages.length) {
          setState(() {
            _selectedImages = validImages;
          });
          debugPrint(
            'Filtered ${_selectedImages.length - validImages.length} invalid images',
          );
        }
      } else {
        // For mobile, convert all images to paths (including searched images)
        final validImages = <MediaFile>[];
        final allPaths = <String>[];

        for (final img in _selectedImages) {
          // Check if image has valid path (normal gallery images)
          if (img.path != null && img.path!.isNotEmpty) {
            validImages.add(img);
            allPaths.add(img.path!);
            debugPrint('Added path-based image: ${img.path}');
          }
          // For searched images with bytes, convert to temporary file
          else if (img.bytes != null &&
              img.bytes!.isNotEmpty &&
              img.bytes!.length > 50) {
            try {
              // Create temporary file from bytes
              final tempPath = await _createTempFileFromBytes(
                img.bytes!,
                img.name,
              );
              if (tempPath != null) {
                // Create new MediaFile with path
                final pathBasedImg = MediaFile(
                  path: tempPath,
                  bytes: null,
                  name: img.name,
                  type: img.type,
                );
                validImages.add(pathBasedImg);
                allPaths.add(tempPath);
                debugPrint(
                  'Converted byte-based image to temp file: $tempPath',
                );
              } else {
                debugPrint('Failed to create temp file for: ${img.name}');
              }
            } catch (e) {
              debugPrint('Error converting bytes to file for ${img.name}: $e');
            }
          } else {
            debugPrint(
              'Skipping image with no valid path or bytes: ${img.name}',
            );
          }
        }

        if (validImages.isEmpty) {
          toastification.show(
            alignment: Alignment.topCenter,
            title: Text('No Valid Images'),
            description: Text(
              'No valid images found. Please try selecting images again.',
            ),
            type: ToastificationType.error,
            style: ToastificationStyle.flatColored,
            autoCloseDuration: Duration(seconds: 3),
          );
          setState(() {
            _isUpdating = false;
          });
          return;
        }

        // All images are now path-based for mobile
        imagePaths = allPaths;
        debugPrint('Mobile: All ${allPaths.length} images converted to paths');

        // Update selected images list
        setState(() {
          _selectedImages = validImages;
        });
      }
    }

    // Handle video selection
    if (_selectedVideoAds.isNotEmpty) {
      final firstVideoAd = _selectedVideoAds.first;
      if (kIsWeb) {
        videoBytes = firstVideoAd.bytes;
      } else {
        videoPath = firstVideoAd.path;
      }
    } else if (_selectedVideos.isNotEmpty) {
      final firstVideo = _selectedVideos.first;
      if (kIsWeb) {
        videoBytes = firstVideo.bytes;
      } else {
        videoPath = firstVideo.path;
      }
    }
    //

    debugPrint('=== UPLOAD DEBUG INFO ===');
    debugPrint('Selected images count: ${_selectedImages.length}');
    debugPrint('kIsWeb: $kIsWeb');

    for (int i = 0; i < _selectedImages.length; i++) {
      final img = _selectedImages[i];
      debugPrint('Image $i:');
      debugPrint('  - has bytes: ${img.bytes != null}');
      debugPrint('  - bytes length: ${img.bytes?.length}');
      debugPrint('  - has path: ${img.path != null}');
      debugPrint('  - path: ${img.path}');
      debugPrint('  - name: ${img.name}');
      debugPrint('  - type: ${img.type}');
    }

    debugPrint('imagePaths: $imagePaths');
    debugPrint('imageBytes count: ${imageBytes?.length}');
    debugPrint('videoPath: $videoPath');
    debugPrint('videoBytes: ${videoBytes != null}');
    debugPrint('pollQuestion: $_pollQuestion');
    debugPrint('=== END DEBUG INFO ===');
    try {
      await postCubit.createPost(
        newPost,
        imagePaths: imagePaths,
        imageBytes: imageBytes,
        videoPath: videoPath,
        videoBytes: videoBytes,
        pollQuestion: _pollQuestion,
      );
      _handlePostSuccess();
      // show success toast
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Success'),
        description: Text('PutUp created successfully!'),
        type: ToastificationType.success,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
    } catch (e) {
      _handlePostError(e.toString());

      // Offer retry option for certain types of errors
      if (e.toString().contains('network') ||
          e.toString().contains('connection') ||
          e.toString().contains('upload failed')) {
        _showRetryOption(
          newPost,
          imagePaths,
          imageBytes,
          videoPath,
          videoBytes,
        );
      }
    }
  }

  void _handlePostSuccess() {
    // Clear draft
    homePageKey.currentState?.switchTab(0);
    _clearDraft();
    // Clear all state
    _textController.clear();
    setState(() {
      _selectedImages.clear();
      _selectedVideos.clear();
      _selectedVideoAds.clear();
      _links.clear();
      _pollQuestion = null;
      _isUpdating = false; // Reset updating state
    });
  }

  void _handlePostError(String message) {
    final postCost = context.read<PostCostCubit>().state;

    // Rollback optimistic wallet deduction
    context.read<WalletBalanceCubit>().addOptimistically(postCost);

    setState(() {
      _isUpdating = false; // Reset updating state on error
    });

    // Show detailed error message
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text('Upload Failed'),
      description: Text(message),
      type: ToastificationType.error,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: Duration(
        seconds: 5,
      ), // Longer duration for error messages
    );

    // Log error for debugging
    debugPrint('Post upload error: $message');
  }

  void _showRetryOption(
    Post post,
    List<String>? imagePaths,
    List<Uint8List>? imageBytes,
    String? videoPath,
    Uint8List? videoBytes,
  ) {
    // Show a simple retry notification
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text('Upload Failed'),
      description: Text('Network error detected. You can try uploading again.'),
      type: ToastificationType.warning,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: Duration(seconds: 8),
    );

    // The user can simply tap the PutUp button again to retry
    // since we've already reset the _isUpdating state
  }

  void _showProgressToast(String stage, double progress) {
    final progressPercent = (progress * 100).round();
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text('Creating PutUp...'),
      description: Text('$stage ($progressPercent%)'),
      type: ToastificationType.info,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: Duration(seconds: 2),
    );
  }

  Widget _buildOptimizedTextField() {
    return TextField(
      controller: _textController,
      focusNode: _focusNode, // Use the focus node
      maxLines: null,
      expands: true,
      maxLength: 480,
      onChanged: (text) {
        // Debounce draft saving to prevent excessive rebuilds
        _debounceDraftSave(text);
      },
      decoration: InputDecoration(
        hintText: 'Write something...',
        border: InputBorder.none,
        hintStyle: TextStyle(color: Theme.of(context).colorScheme.outline),
      ),
    );
  }

  // ---------------- UI ----------------
  // Show emoji picker
  void _showEmojiPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Choose Emoji',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
            // Emoji picker
            Expanded(
              child: EmojiPicker(
                onEmojiSelected: (category, emoji) {
                  // Insert emoji at cursor position
                  final text = _textController.text;
                  final selection = _textController.selection;
                  final newText = text.replaceRange(
                    selection.start,
                    selection.end,
                    emoji.emoji,
                  );
                  _textController.text = newText;
                  _textController.selection = TextSelection.collapsed(
                    offset: selection.start + emoji.emoji.length,
                  );
                  Navigator.pop(context);
                  // Restore focus after emoji selection
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      _focusNode.requestFocus();
                    }
                  });
                },
                config: Config(
                  height: 256,
                  checkPlatformCompatibility: true,
                  emojiViewConfig: EmojiViewConfig(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    columns: 7,
                    emojiSizeMax: 28,
                  ),
                  skinToneConfig: const SkinToneConfig(),
                  categoryViewConfig: CategoryViewConfig(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    iconColorSelected: Theme.of(context).colorScheme.primary,
                    iconColor: Theme.of(context).colorScheme.outline,
                  ),
                  bottomActionBarConfig: BottomActionBarConfig(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    buttonColor: Theme.of(context).colorScheme.surface,
                    buttonIconColor: Theme.of(context).colorScheme.primary,
                  ),
                  searchViewConfig: SearchViewConfig(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    buttonIconColor: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ).then((_) {
      // Restore focus when modal is dismissed
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _focusNode.requestFocus();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PostCubit, PostStates>(
      listener: (context, state) {
        // if (state is PostsLoaded && _isUpdating) {
        //   _handlePostSuccess();
        // }
        // if (state is PostsError && _isUpdating) {
        //   _handlePostError(state.message);
        // }
        // // if (state is PostsUploadingProgress) {
        // //   _showProgressToast(state.stage, state.progress);
        // // }
      },
      builder: (context, state) {
        return buildUploadPage();
      },
    );
  }

  // Replace the buildUploadPage() method with this updated version

  Widget buildUploadPage() {
    return ConstrainedScaffold(
      resizeToAvoidBottomInset: false,
      body: Column(
        children: [
          // Scrollable content area
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
                top: 16,
                bottom: 16,
              ),
              child: Column(
                children: [
                  // Category at top
                  BlocBuilder<CategoryCubit, PostCategory?>(
                    builder: (context, category) {
                      return Text(
                        category?.name.replaceFirst(
                              category.name[0],
                              category.name[0].toUpperCase(),
                            ) ??
                            'Politics',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 4),

                  // Main content area
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Text input field
                        ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: 150,
                            maxHeight: 200,
                          ),
                          child: _buildOptimizedTextField(),
                        ),

                        // Media preview shows dynamically and pushes buttons down
                        if (_selectedImages.isNotEmpty ||
                            _selectedVideos.isNotEmpty ||
                            _selectedVideoAds.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          _buildMediaPreview(),
                        ],

                        // Poll preview
                        if (_pollQuestion != null) ...[
                          const SizedBox(height: 8),
                          _buildPollPreview(),
                        ],

                        // Links preview
                        if (_links.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          _buildLinksPreview(),
                        ],

                        // Media pill buttons - directly attached to input with minimal space
                        const SizedBox(height: 8),
                        _buildMediaPillButtons(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Fixed bottom section for cost and upload button
          Container(
            padding: EdgeInsets.fromLTRB(
              16,
              8,
              16,
              MediaQuery.of(context).viewInsets.bottom + 16,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.background,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: _buildCostAndUploadSection(),
          ),
        ],
      ),
    );
  }

  // Also update the _buildMediaPillButtons method to remove bottom spacing

  Widget _buildMediaPillButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Add Content',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        MediaPillButtonRow(
          onImagesSelected: _handleImagesSelected,
          onVideoSelected: _handleVideoSelected,
          onVideoAdSelected: _handleVideoAdSelected,
          onPollSelected: _handlePollSelected,
          onImageSearchSelected: () {
            ImageSearchBottomSheet.show(
              context,
              onImagesSelected: _handleImagesSelected,
            );
          },
          onLinksSelected: () {
            LinkManagementBottomSheet.show(
              context,
              initialLinks: _links,
              onLinksChanged: _updateLinks,
            );
          },
          onEmojiSelected: _showEmojiPicker,
          hasImages: _selectedImages.isNotEmpty,
          hasVideo: _selectedVideos.isNotEmpty,
          hasVideoAd: _selectedVideoAds.isNotEmpty,
          hasPoll: _pollQuestion != null,
          hasLinks: _links.isNotEmpty,
          hasImageSearch: false,
        ),
        // Removed the bottom SizedBox(height: 4) to eliminate extra space
      ],
    );
  }

  Widget _buildMediaPreview() {
    return Container(
      padding: const EdgeInsets.all(2),

      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_selectedImages.isNotEmpty) ...[
            // Text(
            //   'Images (${_selectedImages.length})',
            //   style: Theme.of(
            //     context,
            //   ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            // ),
            // const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  final image = _selectedImages[index];
                  return Container(
                    width: 40,
                    margin: const EdgeInsets.only(right: 8),
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildImagePreview(image),
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              padding: const EdgeInsets.all(4),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 8,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
          if (_selectedVideos.isNotEmpty) ...[
            if (_selectedImages.isNotEmpty) const SizedBox(height: 8),
            // Text(
            //   'Videos (${_selectedVideos.length})',
            //   style: Theme.of(
            //     context,
            //   ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            // ),
            const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedVideos.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 40,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: Colors.black12,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Stack(
                      children: [
                        Center(
                          child: Icon(
                            Icons.play_circle_outline,
                            size: 25,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeVideo(index),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              padding: const EdgeInsets.all(4),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 8,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
          if (_selectedVideoAds.isNotEmpty) ...[
            if (_selectedImages.isNotEmpty || _selectedVideos.isNotEmpty)
              const SizedBox(height: 8),
            const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedVideoAds.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 40,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: Colors.deepOrange.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.deepOrange.shade700),
                    ),
                    child: Stack(
                      children: [
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.video_camera_back,
                                size: 16,
                                color: Colors.deepOrange.shade700,
                              ),
                              Text(
                                'AD',
                                style: TextStyle(
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.deepOrange.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Positioned(
                          top: 2,
                          right: 2,
                          child: GestureDetector(
                            onTap: () => _removeVideoAd(index),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              padding: const EdgeInsets.all(2),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 10,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLinksPreview() {
    return Container(
      padding: const EdgeInsets.all(4),

      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...List.generate(_links.length, (index) {
            final link = _links[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 2),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.2),
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.link,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      link,
                      style: Theme.of(context).textTheme.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPollPreview() {
    return PollPreviewWidget(
      question: _pollQuestion!,
      onRemove: () {
        setState(() {
          _pollQuestion = null;
        });
      },
    );
  }

  Widget _buildCostAndUploadSection() {
    return Column(
      children: [
        // Cost display
        BlocBuilder<PostCostCubit, double>(
          builder: (context, cost) {
            return Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Post Cost:',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Text(
                    '\$${cost.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        const SizedBox(height: 12),

        // Upload button with circular progress
        BlocBuilder<PostCubit, PostStates>(
          builder: (context, state) {
            final isUploading =
                state is PostsUploading || state is PostsUploadingProgress;
            final progress = state is PostsUploadingProgress
                ? state.progress
                : 0.0;
            final stage = state is PostsUploadingProgress ? state.stage : '';

            return Container(
              width: MediaQuery.of(context).size.width / 3,
              height: 44,
              child: ElevatedButton(
                onPressed: isUploading || _isUpdating ? null : _uploadPost,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isUploading || _isUpdating
                      ? Colors.grey
                      : Theme.of(context).colorScheme.tertiary,
                  foregroundColor: Theme.of(context).colorScheme.surface,
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(22),
                  ),
                ),
                child: isUploading || _isUpdating
                    ? Stack(
                        alignment: Alignment.center,
                        children: [
                          // Circular progress indicator
                          SizedBox(
                            width: 28,
                            height: 28,
                            child: CircularProgressIndicator(
                              value: progress > 0 ? progress : null,
                              strokeWidth: 2.5,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                              backgroundColor: Colors.white.withOpacity(0.3),
                            ),
                          ),
                        ],
                      )
                    : const Text(
                        'PutUp!',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
              ),
            );
          },
        ),
      ],
    );
  }
}
