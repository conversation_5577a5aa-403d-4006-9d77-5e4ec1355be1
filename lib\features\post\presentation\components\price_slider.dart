import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/custom_icon_button.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_cost_cubit.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_states.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';

class PriceSlider extends StatelessWidget {
  final bool isEditable;

  const PriceSlider({super.key, this.isEditable = false});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WalletCubit, WalletStates>(
      builder: (context, walletState) {
        double minValue = 0.05;
        double maxBalance = minValue;

        if (walletState is WalletLoaded) {
          maxBalance = walletState.wallet.balance > minValue
              ? walletState.wallet.balance
              : minValue;
        }

        return BlocBuilder<PostCostCubit, double>(
          builder: (context, currentCost) {
            // Clamp current cost between min & max to avoid assertion error
            double safeValue = currentCost.clamp(minValue, maxBalance);

            // Calculate divisions safely (avoid 0)
            int divisions = ((maxBalance - minValue) * 20).round().clamp(
              1,
              1000,
            );

            return Container(
              padding: const EdgeInsets.symmetric(vertical: 6),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'PutUp Price',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                    ),
                  ),

                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Theme.of(context).colorScheme.tertiary,
                      inactiveTrackColor: Theme.of(context).colorScheme.outline,
                      thumbColor: Theme.of(context).colorScheme.tertiary,
                      overlayColor: Theme.of(
                        context,
                      ).colorScheme.tertiary.withAlpha(50),
                      valueIndicatorColor: Theme.of(
                        context,
                      ).colorScheme.primary,
                      valueIndicatorTextStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                      trackHeight: 2,
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 8,
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 12,
                      ),
                    ),

                    child: Slider(
                      value: safeValue,
                      min: minValue,
                      max: maxBalance,
                      divisions: divisions,
                      onChanged: (value) {
                        context.read<PostCostCubit>().updateCost(value);
                        _updateUserProfilePostAmount(context, value);
                      },
                    ),
                  ),

                  // Cost bubble (tap to open modal in both modes)
                  GestureDetector(
                    onTap: isEditable
                        ? () => _showUpdateModal(
                            context,
                            safeValue,
                            minValue,
                            maxBalance,
                          )
                        : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.tertiary,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '\$ ${safeValue.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Balance warning
                  if (walletState is WalletLoaded &&
                      safeValue > walletState.wallet.balance)
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning,
                            size: 16,
                            color: Theme.of(
                              context,
                            ).colorScheme.onErrorContainer,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              'Low balance: \$${walletState.wallet.balance.toStringAsFixed(2)}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onErrorContainer,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _updateUserProfilePostAmount(BuildContext context, double value) {
    // Get current user
    final authCubit = context.read<AuthCubit>();
    final currentUser = authCubit.currentUser;

    if (currentUser != null) {
      // Update the user's profile with the new post amount
      context.read<CurrentUserProfileCubit>().updateCurrentUserProfile(
        uid: currentUser.uid,
        newPostAmount: value,
      );
    }
  }

  void _showUpdateModal(
    BuildContext context,
    double currentValue,
    double minValue,
    double maxValue,
  ) {
    final TextEditingController controller = TextEditingController(
      text: currentValue.toStringAsFixed(2),
    );

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.fromLTRB(16, 16, 16, MediaQuery.of(context).viewInsets.bottom + 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Update Post Cost",
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: controller,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                decoration: InputDecoration(
                  labelText:
                      "Enter amount (\$$minValue - \$${maxValue.toStringAsFixed(2)})",
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              CustomIconButton(
                onTap: () {
                  final entered =
                      double.tryParse(controller.text) ?? currentValue;
                  final clamped = entered.clamp(minValue, maxValue);

                  context.read<PostCostCubit>().updateCost(clamped);
                  _updateUserProfilePostAmount(context, clamped);

                  Navigator.pop(context);
                },
                label: 'Save',
                icon: Icons.wallet_membership,
                backgroundColor: Theme.of(context).colorScheme.tertiary,
              ),
            ],
          ),
        );
      },
    );
  }
}
