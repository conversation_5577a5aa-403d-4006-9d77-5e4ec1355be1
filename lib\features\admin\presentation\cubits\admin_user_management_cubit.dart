import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/admin/domain/repos/admin_user_management_repo.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_user_management_state.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';

class AdminUserManagementCubit extends Cubit<AdminUserManagementState> {
  final AdminUserManagementRepo userManagementRepo;

  AdminUserManagementCubit({required this.userManagementRepo})
    : super(AdminUserManagementInitial());

  // Load all users
  Future<void> loadUsers() async {
    emit(AdminUserManagementLoading());
    try {
      final users = await userManagementRepo.getAllUsers();
      emit(AdminUserManagementLoaded(users: users));
    } catch (e) {
      emit(AdminUserManagementError('Failed to load users: $e'));
    }
  }

  // Search users
  Future<void> searchUsers(String query) async {
    if (query.isEmpty) {
      await loadUsers();
      return;
    }

    emit(AdminUserManagementLoading());
    try {
      final users = await userManagementRepo.searchUsers(query);
      emit(AdminUserManagementLoaded(users: users));
    } catch (e) {
      emit(AdminUserManagementError('Failed to search users: $e'));
    }
  }

  // Get user by ID
  Future<void> getUserById(String userId) async {
    emit(AdminUserManagementLoading());
    try {
      final user = await userManagementRepo.getUserById(userId);
      if (user != null) {
        emit(AdminUserManagementUserSelected(user: user));
      } else {
        emit(AdminUserManagementError('User not found'));
      }
    } catch (e) {
      emit(AdminUserManagementError('Failed to get user: $e'));
    }
  }

  // Toggle user block status
  Future<void> toggleUserBlockStatus(String userId, bool isBlocked) async {
    try {
      await userManagementRepo.toggleUserBlockStatus(userId, isBlocked);

      // Reload users to reflect changes
      if (state is AdminUserManagementLoaded) {
        await loadUsers();
      }
    } catch (e) {
      emit(AdminUserManagementError('Failed to toggle block status: $e'));
    }
  }

  // Delete user
  Future<void> deleteUser(String userId) async {
    try {
      await userManagementRepo.deleteUser(userId);

      // Reload users to reflect changes
      if (state is AdminUserManagementLoaded) {
        await loadUsers();
      }
    } catch (e) {
      emit(AdminUserManagementError('Failed to delete user: $e'));
    }
  }

  // Create new user
  Future<void> createUser({
    required String email,
    required String name,
    String? bio,
    bool isAdmin = false,
  }) async {
    emit(AdminUserManagementLoading());
    try {
      await userManagementRepo.createUser(
        email: email,
        name: name,
        bio: bio,
        isAdmin: isAdmin,
      );

      // Reload users to show the new user
      await loadUsers();
    } catch (e) {
      emit(AdminUserManagementError('Failed to create user: $e'));
    }
  }

  // Update user profile
  Future<void> updateUserProfile(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await userManagementRepo.updateUserProfile(userId, updates);

      // Reload users to reflect changes
      if (state is AdminUserManagementLoaded) {
        await loadUsers();
      }
    } catch (e) {
      emit(AdminUserManagementError('Failed to update user profile: $e'));
    }
  }

  // Get user statistics
  Future<void> getUserStats(String userId) async {
    try {
      final stats = await userManagementRepo.getUserStats(userId);
      emit(AdminUserManagementStatsLoaded(stats: stats));
    } catch (e) {
      emit(AdminUserManagementError('Failed to get user stats: $e'));
    }
  }

  // Load blocked users
  Future<void> loadBlockedUsers() async {
    emit(AdminUserManagementLoading());
    try {
      final users = await userManagementRepo.getBlockedUsers();
      emit(AdminUserManagementLoaded(users: users));
    } catch (e) {
      emit(AdminUserManagementError('Failed to load blocked users: $e'));
    }
  }

  // Clear error state
  void clearError() {
    if (state is AdminUserManagementError) {
      // Return to previous state or initial state
      emit(AdminUserManagementInitial());
    }
  }
}
