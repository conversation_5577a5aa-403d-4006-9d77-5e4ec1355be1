import 'package:flutter/foundation.dart';
import 'package:moneymouthy/features/wallet/domain/repos/payment_repo.dart';
import 'package:moneymouthy/features/wallet/services/stripe_service.dart';

class StripePaymentRepo implements PaymentRepo {
  @override
  Future<bool> processPayment(double amount) async {
    try {
      bool result = false;
      if (kIsWeb) {
        await StripeService.processWebPayment(amount);
      }
      result = await StripeService.processPayment(amount);

      return result;
    } catch (e) {
      throw Exception('Failed to process payment: $e');
    }
  }
}
