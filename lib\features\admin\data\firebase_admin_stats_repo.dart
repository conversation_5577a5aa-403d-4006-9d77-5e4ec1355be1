/*
Firebase Implementation of Admin Statistics Repository
*/

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:moneymouthy/features/admin/domain/repos/admin_stats_repo.dart';

class FirebaseAdminStatsRepo implements AdminStatsRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;

  @override
  Future<int> getTotalUsersCount() async {
    try {
      final querySnapshot = await firebaseFirestore.collection('users').get();
      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get total users count: $e');
    }
  }

  @override
  Future<int> getTotalPostsCount() async {
    try {
      final querySnapshot = await firebaseFirestore
          .collection('posts_new')
          .get();
      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get total posts count: $e');
    }
  }

  @override
  Future<double> getTotalWalletBalance() async {
    try {
      final querySnapshot = await firebaseFirestore
          .collection('wallets_new')
          .get();
      double totalBalance = 0.0;

      for (final doc in querySnapshot.docs) {
        final balance = doc.data()['balance'] ?? 0.0;
        totalBalance += balance;
      }

      return totalBalance;
    } catch (e) {
      throw Exception('Failed to get total wallet balance: $e');
    }
  }

  @override
  Future<Map<String, int>> getPostsCountByCategory() async {
    try {
      final querySnapshot = await firebaseFirestore
          .collection('posts_new')
          .get();
      final categoryCount = <String, int>{};

      for (final doc in querySnapshot.docs) {
        final category = doc.data()['category'] ?? 'politics';
        categoryCount[category] = (categoryCount[category] ?? 0) + 1;
      }

      return categoryCount;
    } catch (e) {
      throw Exception('Failed to get posts count by category: $e');
    }
  }

  @override
  Future<int> getRecentUserRegistrations() async {
    try {
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      final querySnapshot = await firebaseFirestore
          .collection('users')
          .where('createdAt', isGreaterThan: Timestamp.fromDate(sevenDaysAgo))
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get recent user registrations: $e');
    }
  }

  @override
  Future<int> getRecentPostsCount() async {
    try {
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      final querySnapshot = await firebaseFirestore
          .collection('posts_new')
          .where('timestamp', isGreaterThan: Timestamp.fromDate(sevenDaysAgo))
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get recent posts count: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getTopPosts({int limit = 5}) async {
    try {
      final querySnapshot = await firebaseFirestore
          .collection('posts_new')
          .orderBy('likes', descending: true)
          .limit(limit * 2) // Get more to sort by engagement
          .get();

      final posts = querySnapshot.docs.map((doc) {
        final data = doc.data();
        final likes = (data['likes'] as List?)?.length ?? 0;
        final hates = (data['hates'] as List?)?.length ?? 0;
        final engagement = likes + hates;

        return {
          'id': doc.id,
          'userName': data['userName'] ?? 'Unknown',
          'text': data['text'] ?? '',
          'likes': likes,
          'hates': hates,
          'engagement': engagement,
          'timestamp': data['timestamp'],
        };
      }).toList();

      // Sort by engagement (likes + hates)
      posts.sort(
        (a, b) => (b['engagement'] as int).compareTo(a['engagement'] as int),
      );

      return posts.take(limit).toList();
    } catch (e) {
      throw Exception('Failed to get top posts: $e');
    }
  }

  // New method to get total Stripe balance after 30/8/2025
  Future<double> getTotalStripeBalance() async {
    try {
      // Create date for 30/8/2025
      final cutoffDate = DateTime(2025, 8, 30);

      final querySnapshot = await firebaseFirestore
          .collection('transactions_new')
          .where('methodType', isEqualTo: 'Stripe')
          .where('timestamp', isGreaterThan: Timestamp.fromDate(cutoffDate))
          .get();

      double totalStripeBalance = 0.0;

      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final amount = (data['amount'] ?? 0).toDouble();
        final type = data['type'] ?? '';

        // Add for deposits, subtract for withdrawals
        if (type == 'deposit') {
          totalStripeBalance += amount;
        } else if (type == 'withdrawal') {
          totalStripeBalance -= amount;
        }
      }

      return totalStripeBalance;
    } catch (e) {
      throw Exception('Failed to get total Stripe balance: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getSystemStats() async {
    try {
      final usersCount = await getTotalUsersCount();
      final postsCount = await getTotalPostsCount();
      final totalBalance = await getTotalWalletBalance();
      final totalStripeBalance = await getTotalStripeBalance();
      final postsByCategory = await getPostsCountByCategory();
      final recentUsers = await getRecentUserRegistrations();
      final recentPosts = await getRecentPostsCount();

      // Get blocked users count
      final blockedUsersQuery = await firebaseFirestore
          .collection('users')
          .where('isBlocked', isEqualTo: true)
          .get();

      // Get total transactions count
      final transactionsQuery = await firebaseFirestore
          .collection('transactions_new')
          .get();

      return {
        'totalUsers': usersCount,
        'totalPosts': postsCount,
        'totalBalance': totalBalance,
        'totalStripeBalance': totalStripeBalance,
        'postsByCategory': postsByCategory,
        'recentUsers': recentUsers,
        'recentPosts': recentPosts,
        'blockedUsers': blockedUsersQuery.docs.length,
        'totalTransactions': transactionsQuery.docs.length,
      };
    } catch (e) {
      throw Exception('Failed to get system stats: $e');
    }
  }
}
