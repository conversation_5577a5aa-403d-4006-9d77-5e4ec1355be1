class AppUser {
  final String uid;
  final String name;
  final String email;

  AppUser({required this.uid, required this.name, required this.email});

  // Convert to json
  Map<String, dynamic> to<PERSON>son() {
    return {'uid': uid, 'name': name, 'email': email};
  }

  // Convert from json
  factory AppUser.fromJson(Map<String, dynamic> jsonUser) {
    return AppUser(
      uid: jsonUser['uid'],
      name: json<PERSON>ser['name'],
      email: jsonUser['email'],
    );
  }
}
