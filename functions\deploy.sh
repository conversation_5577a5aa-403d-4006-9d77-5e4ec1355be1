#!/bin/bash

# Firebase Functions Deployment Script
echo "🚀 Deploying Firebase Functions..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Install with: npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Run: firebase login"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Deploy functions
echo "🔥 Deploying functions to Firebase..."
firebase deploy --only functions

# Check deployment status
if [ $? -eq 0 ]; then
    echo "✅ Functions deployed successfully!"
    echo "📋 Available functions:"
    echo "   - cleanupOldPostsAndMedia (runs every hour)"
    echo "   - manualCleanup (for testing)"
    echo "   - createPaymentIntent"
    echo "   - handlePaymentCompletion"
    echo "   - createCheckoutSession"
    echo "   - handleStripeWebhook"
    echo "   - proxyRequest"
else
    echo "❌ Deployment failed!"
    exit 1
fi

echo ""
echo "📊 To monitor cleanup activity:"
echo "   firebase functions:log --only cleanupOldPostsAndMedia"
echo ""
echo "🧪 To test manual cleanup:"
echo "   curl -X POST [function-url] -H 'Content-Type: application/json' -d '{\"hours\": 48}'"