# Android
releases/
android/build/reports/problems/problems-report.html
releases/app-release.aab
*.aab

# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ-related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
.vscode/

# Web-related
node_modules/

# Flutter-related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication-related
app.*.symbols

# Obfuscation-related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# # Firebase related
# */google-services.json
# */firebase_options.dart
# */firebase-debug.log

# # Environment files
# .env
# .env.*

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Coverage reports
coverage/
test/coverage_helper_test.dart

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Platform specific
ios/build/*
macos/build/*
linux/build/*
windows/build/*
