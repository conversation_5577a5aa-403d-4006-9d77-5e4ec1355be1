/*
Login Here

On this Page an existing user can login with  their:
  - email
  - password

  Once the user is successfully logged in, the user is redirected to the home page.
  IF user does not have an account yet, it will be redirected to register page to create one.
 */

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:moneymouthy/features/auth/presentation/components/my_button.dart';
import 'package:moneymouthy/features/auth/presentation/components/my_text_field.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:toastification/toastification.dart';

class LoginPage extends StatefulWidget {
  final VoidCallback? togglePages;
  const LoginPage({super.key, this.togglePages});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  // login button pressed
  void login() {
    // Prevent multiple login attempts if already loading
    final currentState = context.read<AuthCubit>().state;
    if (currentState is AuthLoading) {
      return;
    }

    // prepare email and pw
    final String email = emailController.text.trim();
    final String pw = passwordController.text;
    // auth cubit
    final authCubit = context.read<AuthCubit>();

    //   email and password empty validation
    if (email.isNotEmpty && pw.isNotEmpty) {
      authCubit.login(email, pw);
    } else {
      toastification.show(
        alignment: Alignment.topCenter,
        title: const Text('Validation Failed'),
        description: const Text('Please enter both email and password'),
        type: ToastificationType.info,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: const Duration(seconds: 3),
      );
    }
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthError) {
          // Show error message only
          toastification.show(
            alignment: Alignment.topCenter,
            title: const Text('Login Failed'),
            description: Text(state.message),
            type: ToastificationType.error,
            style: ToastificationStyle.flatColored,
            autoCloseDuration: const Duration(seconds: 4),
          );
        }
      },
      child: ConstrainedScaffold(
        resizeToAvoidBottomInset: true,
        // Body
        body: SingleChildScrollView(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: ConstrainedBox(
            constraints: BoxConstraints(minHeight: MediaQuery.of(context).size.height - kToolbarHeight),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 25.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                // logo
                Icon(
                  Icons.lock_open_rounded,
                  size: 80,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 25),
                // welcome back msg
                Text(
                  "Welcome Back, you've been missed!",
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 25),
                // email textfield
                MyTextField(
                  controller: emailController,
                  hintText: 'Email',
                  obscureText: false,
                ),
                const SizedBox(height: 25),

                // password textfield
                MyTextField(
                  controller: passwordController,
                  hintText: 'Password',
                  obscureText: true,
                ),
                const SizedBox(height: 20),

                // login button with loading state
                BlocBuilder<AuthCubit, AuthState>(
                  builder: (context, state) {
                    final isLoading = state is AuthLoading;
                    return MyButton(
                      onTap: login,
                      text: isLoading ? 'Logging in...' : 'Login',
                      isLoading: isLoading,
                    );
                  },
                ),
                // not a member? register now
                const SizedBox(height: 50),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Not a member?',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () {
                        if (widget.togglePages != null) {
                          // If we have toggle function (from AuthPage), use it
                          widget.togglePages!();
                        } else {
                          // Otherwise navigate to signup route (from Landing page)
                          context.go('/signup');
                        }
                      },
                      child: Text(
                        'Register Now',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        ),
        ),
      ),
    );
  }
}
