/*
Firebase Implementation of Admin User Management Repository
*/

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:moneymouthy/features/admin/domain/repos/admin_user_management_repo.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';

class FirebaseAdminUserManagementRepo implements AdminUserManagementRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;

  @override
  Future<List<ProfileUser>> getAllUsers() async {
    try {
      final querySnapshot = await firebaseFirestore.collection('users').get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['uid'] = doc.id; // Add the document ID as uid
        return ProfileUser.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get all users: $e');
    }
  }

  @override
  Future<List<ProfileUser>> searchUsers(String query) async {
    try {
      final querySnapshot = await firebaseFirestore
          .collection('users')
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      final users = querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['uid'] = doc.id;
        return ProfileUser.fromJson(data);
      }).toList();

      // Also search by email
      final emailQuerySnapshot = await firebaseFirestore
          .collection('users')
          .where('email', isGreaterThanOrEqualTo: query)
          .where('email', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      final emailUsers = emailQuerySnapshot.docs.map((doc) {
        final data = doc.data();
        data['uid'] = doc.id;
        return ProfileUser.fromJson(data);
      }).toList();

      // Combine and remove duplicates
      final allUsers = [...users, ...emailUsers];
      final uniqueUsers = <String, ProfileUser>{};

      for (final user in allUsers) {
        uniqueUsers[user.uid] = user;
      }

      return uniqueUsers.values.toList();
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  @override
  Future<ProfileUser?> getUserById(String userId) async {
    try {
      final doc = await firebaseFirestore.collection('users').doc(userId).get();

      if (!doc.exists) return null;

      final data = doc.data()!;
      data['uid'] = doc.id;
      return ProfileUser.fromJson(data);
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  @override
  Future<void> toggleUserBlockStatus(String userId, bool isBlocked) async {
    try {
      await firebaseFirestore.collection('users').doc(userId).update({
        'isBlocked': isBlocked,
        'blockedAt': isBlocked ? Timestamp.now() : null,
      });
    } catch (e) {
      throw Exception('Failed to toggle user block status: $e');
    }
  }

  @override
  Future<void> deleteUser(String userId) async {
    try {
      // Delete user from Firestore
      await firebaseFirestore.collection('users').doc(userId).delete();

      // Also delete from wallets collection
      await firebaseFirestore.collection('wallets_new').doc(userId).delete();

      // Delete user's posts
      final postsQuery = await firebaseFirestore
          .collection('posts_new')
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in postsQuery.docs) {
        await doc.reference.delete();
      }

      // Note: Firebase Auth user deletion should be handled separately
      // as it requires admin privileges
    } catch (e) {
      throw Exception('Failed to delete user: $e');
    }
  }

  @override
  Future<void> createUser({
    required String email,
    required String name,
    String? bio,
    bool isAdmin = false,
  }) async {
    try {
      // Create user in Firebase Auth
      final userCredential = await firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: 'TempPass123!', // Temporary password
      );

      final userId = userCredential.user!.uid;

      // Create user profile in users collection
      final userData = {
        'uid': userId,
        'email': email,
        'name': name,
        'bio': bio ?? '',
        'profileImageUrl': '',
        'following': [],
        'followers': [],
        'selectedPostCategory': 'politics',
        'postAmount': 0.05,
        'isBlocked': false,
        'createdAt': Timestamp.now(),
      };

      await firebaseFirestore.collection('users').doc(userId).set(userData);

      // If creating an admin user, also add to admins collection
      if (isAdmin) {
        final adminData = {
          'uid': userId,
          'email': email,
          'name': name,
          'role': 'admin',
          'createdAt': Timestamp.now(),
        };
        await firebaseFirestore.collection('admins').doc(userId).set(adminData);
      }

      // Create wallet for the user
      await firebaseFirestore.collection('wallets_new').doc(userId).set({
        'userId': userId,
        'balance': 0.0,
        'createdAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  @override
  Future<void> updateUserProfile(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await firebaseFirestore.collection('users').doc(userId).update(updates);
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getUserStats(String userId) async {
    try {
      // Get user's posts count
      final postsQuery = await firebaseFirestore
          .collection('posts_new')
          .where('userId', isEqualTo: userId)
          .get();

      // Get user's wallet balance
      final walletDoc = await firebaseFirestore
          .collection('wallets_new')
          .doc(userId)
          .get();

      final balance = walletDoc.exists
          ? (walletDoc.data()?['balance'] ?? 0.0)
          : 0.0;

      // Get user's profile
      final userDoc = await firebaseFirestore
          .collection('users')
          .doc(userId)
          .get();
      final followers = userDoc.exists
          ? (userDoc.data()?['followers'] ?? [])
          : [];
      final following = userDoc.exists
          ? (userDoc.data()?['following'] ?? [])
          : [];

      return {
        'postsCount': postsQuery.docs.length,
        'balance': balance,
        'followersCount': followers.length,
        'followingCount': following.length,
      };
    } catch (e) {
      throw Exception('Failed to get user stats: $e');
    }
  }

  @override
  Future<int> getUsersCount() async {
    try {
      final querySnapshot = await firebaseFirestore.collection('users').get();
      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get users count: $e');
    }
  }

  @override
  Future<List<ProfileUser>> getBlockedUsers() async {
    try {
      final querySnapshot = await firebaseFirestore
          .collection('users')
          .where('isBlocked', isEqualTo: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['uid'] = doc.id;
        return ProfileUser.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get blocked users: $e');
    }
  }
}
