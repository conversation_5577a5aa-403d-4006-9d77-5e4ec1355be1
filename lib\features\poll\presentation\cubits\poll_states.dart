import 'package:moneymouthy/features/poll/domain/entities/poll.dart';

abstract class PollStates {}

// Initial state
class PollInitial extends PollStates {}

// Loading state
class PollLoading extends PollStates {}

// Single poll loaded
class PollLoaded extends PollStates {
  final Poll poll;

  PollLoaded(this.poll);
}

// Multiple polls loaded
class PollsLoaded extends PollStates {
  final List<Poll> polls;

  PollsLoaded(this.polls);
}

// Poll created successfully
class PollCreated extends PollStates {
  final Poll poll;

  PollCreated(this.poll);
}

// Poll not found
class PollNotFound extends PollStates {}

// Poll deleted
class PollDeleted extends PollStates {}

// Error state
class PollError extends PollStates {
  final String message;

  PollError(this.message);
}
