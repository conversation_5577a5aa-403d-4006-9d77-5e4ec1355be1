/// This ia a follow and unfollow button
/// To use this widget, you need
/// -> a function of toggle follow
/// -> isFollowing (false -> show follow button | true -> show unfollow button)
library;

import 'package:flutter/material.dart';

class FollowButton extends StatelessWidget {
  final Function()? onToggleFollow;
  final bool isFollowing;
  const FollowButton({
    super.key,
    required this.onToggleFollow,
    required this.isFollowing,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25.0, vertical: 10.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: MaterialButton(
          onPressed: onToggleFollow,

          // padding Inside
          padding: const EdgeInsets.all(25.0),
          // color
          color: isFollowing
              ? Theme.of(context).colorScheme.outline
              : Theme.of(context).colorScheme.tertiary,

          // text
          child: Text(
            isFollowing ? 'Unfollow' : 'Follow',
            style: TextStyle(
              color: isFollowing
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surface,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
