import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:moneymouthy/features/search/domain/repos/search_repo.dart';

class FirebaseSearchRepo implements SearchRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
  @override
  Future<List<ProfileUser>> searchUsers(String query) async {
    try {
      final result = await firebaseFirestore.collection('users').get();

      return result.docs
          .map((doc) {
            final data = doc.data();
            data['uid'] = doc.id;
            return ProfileUser.fromJson(data);
          })
          .where(
            (user) => user.name.toLowerCase().contains(query.toLowerCase()),
          )
          .toList();
    } catch (e) {
      throw Exception("Failed to search users: $e");
    }
  }
}
