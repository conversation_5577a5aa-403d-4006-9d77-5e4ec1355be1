/*
Admin User Management States
*/

import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';

abstract class AdminUserManagementState {}

class AdminUserManagementInitial extends AdminUserManagementState {}

class AdminUserManagementLoading extends AdminUserManagementState {}

class AdminUserManagementLoaded extends AdminUserManagementState {
  final List<ProfileUser> users;

  AdminUserManagementLoaded({required this.users});
}

class AdminUserManagementUserSelected extends AdminUserManagementState {
  final ProfileUser user;

  AdminUserManagementUserSelected({required this.user});
}

class AdminUserManagementStatsLoaded extends AdminUserManagementState {
  final Map<String, dynamic> stats;

  AdminUserManagementStatsLoaded({required this.stats});
}

class AdminUserManagementError extends AdminUserManagementState {
  final String message;

  AdminUserManagementError(this.message);
}
