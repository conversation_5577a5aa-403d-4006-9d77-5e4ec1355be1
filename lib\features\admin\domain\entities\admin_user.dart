import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';

class AdminUser extends AppUser {
  /// Extra attribute for admin role
  final bool isAdmin;

  AdminUser({
    this.isAdmin = false,
    required super.uid,
    required super.name,
    required super.email,
  });

  /// CopyWith method
  AdminUser copyWith({
    String? uid,
    String? name,
    String? email,
    bool? isAdmin,
  }) {
    return AdminUser(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }

  /// JSON serialization
  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      uid: json['uid'],
      name: json['name'],
      email: json['email'],
      isAdmin: json['isAdmin'] as bool? ?? false,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {'uid': uid, 'name': name, 'email': email, 'isAdmin': isAdmin};
  }
}
