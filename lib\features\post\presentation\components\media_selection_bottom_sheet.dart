import 'package:flutter/material.dart';
import 'package:moneymouthy/features/post/presentation/services/media_selection_service.dart';

class MediaSelectionBottomSheet extends StatelessWidget {
  final Function(List<MediaFile>) onImagesSelected;
  final Function(MediaFile) onVideoSelected;
  final bool allowMultipleImages;
  final bool allowVideo;
  final bool appendMode; // New parameter for append functionality

  const MediaSelectionBottomSheet({
    super.key,
    required this.onImagesSelected,
    required this.onVideoSelected,
    this.allowMultipleImages = true,
    this.allowVideo = true,
    this.appendMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 20,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          Text(
            'Select Media',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),

          // Image options
          _buildSectionTitle(context, 'Images'),
          const SizedBox(height: 10),
          Row(
            children: [
              _buildOptionTile(
                context,
                icon: Icons.camera_alt,
                title: 'Camera',
                subtitle: 'Take a photo',
                backgroundColor: Colors.blue.shade900,
                onTap: () async {
                  Navigator.pop(context);
                  final image =
                      await MediaSelectionService.captureImageFromCamera();
                  if (image != null) {
                    onImagesSelected([image]);
                  }
                },
              ),
              const SizedBox(width: 10),
              _buildOptionTile(
                context,
                icon: Icons.photo_library,
                title: 'Gallery',
                subtitle: allowMultipleImages
                    ? 'Select photos'
                    : 'Select photo',
                backgroundColor: Colors.green.shade900,
                onTap: () async {
                  Navigator.pop(context);
                  if (allowMultipleImages) {
                    final images =
                        await MediaSelectionService.selectMultipleImagesFromGallery();
                    if (images.isNotEmpty) {
                      onImagesSelected(images);
                    }
                  } else {
                    final image =
                        await MediaSelectionService.selectImageFromGallery();
                    if (image != null) {
                      onImagesSelected([image]);
                    }
                  }
                },
              ),
            ],
          ),

          if (allowVideo) ...[
            const SizedBox(height: 20),
            _buildSectionTitle(context, 'Video'),
            const SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: _buildOptionTile(
                    context,
                    icon: Icons.videocam,
                    title: 'Record',
                    subtitle: 'Record video',
                    backgroundColor: Colors.red.shade900,
                    onTap: () async {
                      Navigator.pop(context);
                      final video =
                          await MediaSelectionService.recordVideoFromCamera();
                      if (video != null) {
                        onVideoSelected(video);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: _buildOptionTile(
                    context,
                    icon: Icons.video_library,
                    title: 'Gallery',
                    subtitle: 'Select video',
                    backgroundColor: Colors.orange.shade900,
                    onTap: () async {
                      Navigator.pop(context);
                      final video =
                          await MediaSelectionService.selectVideoFromGallery();
                      if (video != null) {
                        onVideoSelected(video);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color backgroundColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(50),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 16, // Smaller icon size
              color: Theme.of(context).colorScheme.surface,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.surface,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.surface.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  static void show(
    BuildContext context, {
    required Function(List<MediaFile>) onImagesSelected,
    required Function(MediaFile) onVideoSelected,
    bool allowMultipleImages = true,
    bool allowVideo = true,
    bool appendMode = false,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MediaSelectionBottomSheet(
        onImagesSelected: onImagesSelected,
        onVideoSelected: onVideoSelected,
        allowMultipleImages: allowMultipleImages,
        allowVideo: allowVideo,
        appendMode: appendMode,
      ),
    );
  }
}
