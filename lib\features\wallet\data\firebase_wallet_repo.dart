import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart'
    as wallet_transaction;
import 'package:moneymouthy/features/wallet/domain/entities/wallet.dart';
import 'package:moneymouthy/features/wallet/domain/repos/wallet_repo.dart';
import 'package:toastification/toastification.dart';

import '../../../globals.dart';

class FirebaseWalletRepo implements WalletRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;

  final CollectionReference walletsCollection = FirebaseFirestore.instance
      .collection('wallets_new');
  final CollectionReference transactionsCollection = FirebaseFirestore.instance
      .collection('transactions_new');

  @override
  Future<Wallet> getWallet(String userId) async {
    try {
      final walletDoc = await walletsCollection.doc(userId).get();
      if (walletDoc.exists) {
        return Wallet.fromJson(walletDoc.data() as Map<String, dynamic>);
      } else {
        // Create new wallet with kDefaultWalletBalance
        final newWallet = Wallet(
          userId: userId,
          balance: kDefaultWalletBalance,
        );
        await walletsCollection.doc(userId).set(newWallet.toJson());
        return newWallet;
      }
    } catch (e) {
      throw Exception("Failed to get wallet: $e");
    }
  }

  @override
  Future<void> addBalance(
    String userId,
    double amount,
    String description, {
    String? methodType,
  }) async {
    try {
      // Get current wallet
      final wallet = await getWallet(userId);

      // Update balance
      final updatedWallet = wallet.copyWith(balance: wallet.balance + amount);
      await walletsCollection.doc(userId).set(updatedWallet.toJson());

      // Create transaction record
      final transaction = wallet_transaction.Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        amount: amount,
        type: wallet_transaction.TransactionType.deposit,
        description: description,
        methodType: methodType ?? 'Manual Deposit',
        timestamp: DateTime.now(),
      );

      await transactionsCollection
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception("Failed to add balance: $e");
    }
  }

  @override
  Future<List<wallet_transaction.Transaction>> getTransactions(
    String userId,
  ) async {
    try {
      final transactionsSnapshot = await transactionsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      return transactionsSnapshot.docs
          .map(
            (doc) => wallet_transaction.Transaction.fromJson(
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      throw Exception("Failed to get transactions: $e");
    }
  }

  @override
  Future<List<wallet_transaction.Transaction>> getTransactionsByType(
    String userId,
    wallet_transaction.TransactionType type,
  ) async {
    try {
      // Get all transactions for user first, then filter by type
      final transactionsSnapshot = await transactionsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      final allTransactions = transactionsSnapshot.docs
          .map(
            (doc) => wallet_transaction.Transaction.fromJson(
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();

      // Filter by type in memory to avoid composite index requirement
      return allTransactions
          .where((transaction) => transaction.type == type)
          .toList();
    } catch (e) {
      throw Exception("Failed to get transactions by type: $e");
    }
  }

  // add transaction
  @override
  Future<void> addTransaction(
    wallet_transaction.Transaction transaction,
  ) async {
    try {
      await transactionsCollection
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception("Failed to add transaction: $e");
    }
  }

  // remove transaction
  @override
  Future<void> removeTransaction(String transactionId) async {
    try {
      await transactionsCollection.doc(transactionId).delete();
    } catch (e) {
      throw Exception("Failed to remove transaction: $e");
    }
  }

  @override
  Future<void> deductBalance(
    String userId,
    double amount,
    String description,
    String? methodType,
  ) async {
    try {
      // Get current wallet
      final wallet = await getWallet(userId);

      // Update balance
      final updatedWallet = wallet.copyWith(balance: wallet.balance - amount);
      await walletsCollection.doc(userId).set(updatedWallet.toJson());

      // Create transaction record
      final transaction = wallet_transaction.Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        amount: amount,
        type: wallet_transaction.TransactionType.spent,
        description: description,
        methodType: methodType ?? 'Manual Deduct',
        timestamp: DateTime.now(),
      );

      await transactionsCollection
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception("Failed to deduct balance: $e");
    }
  }

  /// Alternative version that also creates wallets for users who don't have them
  /// Requires access to users collection to get all user IDs
  ///
  /// [amount] - Amount to add to each user's balance
  /// [description] - Description for the transaction record
  /// [methodType] - Optional method type for the transaction
  /// [usersCollectionName] - Name of the users collection (default: 'users')
  @override
  Future<void> updateAllUsersBalanceWithCreation(
    double amount,
    String description, {
    String? methodType,
    String usersCollectionName = 'users',
  }) async {
    try {
      // Get all users from users collection
      final usersSnapshot = await firebaseFirestore
          .collection(usersCollectionName)
          .get();

      if (usersSnapshot.docs.isEmpty) {
        return; // No users found
      }

      // Get existing wallets
      final walletsSnapshot = await walletsCollection.get();
      final existingWalletUserIds = walletsSnapshot.docs
          .map((doc) => doc.id)
          .toSet();

      const int batchSize = 400; // Conservative limit for mixed operations
      List<WriteBatch> batches = [];
      WriteBatch currentBatch = firebaseFirestore.batch();
      int operationCount = 0;

      List<wallet_transaction.Transaction> transactionsToAdd = [];

      // Process each user
      for (QueryDocumentSnapshot userDoc in usersSnapshot.docs) {
        final userId = userDoc.id;
        Wallet wallet;

        if (existingWalletUserIds.contains(userId)) {
          // Get existing wallet
          final existingWalletDoc = walletsSnapshot.docs.firstWhere(
            (doc) => doc.id == userId,
          );
          wallet = Wallet.fromJson(
            existingWalletDoc.data() as Map<String, dynamic>,
          );
        } else {
          // Create new wallet
          wallet = Wallet(
            userId: userId,
            balance: kDefaultWalletBalance,
          ); // Default balance
        }

        // Calculate new balance
        final newBalance = wallet.balance + amount;
        final updatedWallet = wallet.copyWith(balance: newBalance);

        // Add wallet operation to batch (create or update)
        if (existingWalletUserIds.contains(userId)) {
          currentBatch.update(
            walletsCollection.doc(userId),
            updatedWallet.toJson(),
          );
        } else {
          currentBatch.set(
            walletsCollection.doc(userId),
            updatedWallet.toJson(),
          );
        }

        // Create transaction record
        final transaction = wallet_transaction.Transaction(
          id: '${DateTime.now().millisecondsSinceEpoch}_${userId}',
          userId: userId,
          amount: amount.abs(),
          type: amount >= 0
              ? wallet_transaction.TransactionType.deposit
              : wallet_transaction.TransactionType.spent,
          description: description,
          methodType:
              methodType ?? (amount >= 0 ? 'Bulk Credit' : 'Bulk Debit'),
          timestamp: DateTime.now(),
        );

        transactionsToAdd.add(transaction);
        operationCount++;

        // Check if we need to start a new batch
        if (operationCount >= batchSize) {
          batches.add(currentBatch);
          currentBatch = firebaseFirestore.batch();
          operationCount = 0;
        }
        // logs in taost
        toastification.show(
          alignment: Alignment.topCenter,
          title: Text(
            'Success with stats : ${batches.length} batches, ${transactionsToAdd.length} transactions',
          ),
          description: Text(
            'User $userId balance updated successfully, new balance: $newBalance',
          ),
          type: ToastificationType.success,
          style: ToastificationStyle.flatColored,
          autoCloseDuration: Duration(seconds: 3),
        );
      }

      // Add the last wallet batch if it has operations
      if (operationCount > 0) {
        batches.add(currentBatch);
      }

      // Create batches for transaction records
      for (int i = 0; i < transactionsToAdd.length; i += batchSize) {
        final batch = firebaseFirestore.batch();
        final chunk = transactionsToAdd.skip(i).take(batchSize);

        for (wallet_transaction.Transaction transaction in chunk) {
          batch.set(
            transactionsCollection.doc(transaction.id),
            transaction.toJson(),
          );
        }

        batches.add(batch);
      }

      // Commit all batches with small delays
      for (int i = 0; i < batches.length; i++) {
        await batches[i].commit();

        // Add delay to prevent rate limiting
        if (i < batches.length - 1) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }
    } catch (e) {
      throw Exception("Failed to update all users balance with creation: $e");
    }
  }
}
