const functions = require("firebase-functions");
const stripe = require("stripe")(functions.config().stripe.secret_key);
const cors = require("cors")({ origin: true });
const admin = require("firebase-admin");

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

exports.createPaymentIntent = functions.https.onRequest(async (req, res) => {
  // Handle CORS preflight requests
  return cors(req, res, async () => {
    try {
      // Set additional CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-Requested-With, Accept, Origin",
        "Access-Control-Max-Age": "86400", // 24 hours
        "Content-Type": "application/json",
      });

      // Handle OPTIONS preflight request
      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      // Only allow POST requests for creating payment intents
      if (req.method !== "POST") {
        return res.status(405).json({
          error: "Method not allowed. Only POST requests are accepted.",
          success: false,
        });
      }

      // Validate request body
      const { amount, currency = "usd", email } = req.body || {};

      if (!amount || !email) {
        return res.status(400).json({
          error: "Missing required fields: amount and email are required.",
          success: false,
        });
      }
      if (typeof amount !== "number") {
        //  Parse
        amount = parseFloat(amount);
        if (amount <= 0) {
          return res.status(400).json({
            error: "Amount must be a positive number.",
            success: false,
          });
        }
      }
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          error: "Invalid email format.",
          success: false,
        });
      }

      // Find or create customer
      let customer;
      try {
        const customers = await stripe.customers.list({ email, limit: 1 });
        customer = customers.data[0]
          ? customers.data[0]
          : await stripe.customers.create({ email });
      } catch (stripeError) {
        console.error("Error handling customer:", stripeError);
        return res.status(500).json({
          error: "Failed to create or retrieve customer.",
          success: false,
        });
      }

      // Create ephemeral key
      let ephemeralKey;
      try {
        ephemeralKey = await stripe.ephemeralKeys.create(
          { customer: customer.id },
          { apiVersion: "2020-08-27" }
        );
      } catch (stripeError) {
        console.error("Error creating ephemeral key:", stripeError);
        return res.status(500).json({
          error: "Failed to create ephemeral key.",
          success: false,
        });
      }

      // Create payment intent
      let paymentIntent;
      try {
        paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount), // Ensure amount is an integer
          currency,
          customer: customer.id,
          automatic_payment_methods: {
            enabled: true,
          },
        });
      } catch (stripeError) {
        console.error("Error creating payment intent:", stripeError);
        return res.status(500).json({
          error: "Failed to create payment intent.",
          success: false,
        });
      }

      // Return successful response
      return res.status(200).json({
        paymentIntent: paymentIntent.client_secret,
        ephemeralKey: ephemeralKey.secret,
        customer: customer.id,
        customerEmail: customer.email,
        success: true,
      });
    } catch (error) {
      console.error("Unexpected error creating payment intent:", error);
      return res.status(500).json({
        error: "An unexpected error occurred. Please try again.",
        success: false,
      });
    }
  });
});

// Handle payment completion and update wallet
exports.handlePaymentCompletion = functions.https.onRequest(
  async (req, res) => {
    return cors(req, res, async () => {
      try {
        // Set CORS headers
        res.set({
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers":
            "Content-Type, Authorization, X-Requested-With, Accept, Origin",
          "Access-Control-Max-Age": "86400",
          "Content-Type": "application/json",
        });

        if (req.method === "OPTIONS") {
          return res.status(200).send();
        }

        if (req.method !== "POST") {
          return res.status(405).json({
            error: "Method not allowed. Only POST requests are accepted.",
            success: false,
          });
        }

        const { paymentIntentId, userId } = req.body || {};

        if (!paymentIntentId || !userId) {
          return res.status(400).json({
            error:
              "Missing required fields: paymentIntentId and userId are required.",
            success: false,
          });
        }

        // Retrieve payment intent from Stripe
        const paymentIntent = await stripe.paymentIntents.retrieve(
          paymentIntentId
        );

        if (paymentIntent.status !== "succeeded") {
          return res.status(400).json({
            error: `Payment not completed. Status: ${paymentIntent.status}`,
            success: false,
          });
        }

        // Calculate amount in dollars (Stripe uses cents)
        const amount = paymentIntent.amount / 100;

        // Get Firestore references
        const db = admin.firestore();
        const walletRef = db.collection("wallets").doc(userId);
        const transactionsRef = walletRef.collection("transactions");

        // Check if this payment has already been processed
        const existingTransaction = await transactionsRef
          .where("externalTransactionId", "==", paymentIntentId)
          .limit(1)
          .get();

        if (!existingTransaction.empty) {
          return res.status(200).json({
            message: "Payment already processed",
            success: true,
            alreadyProcessed: true,
          });
        }

        // Use a transaction to ensure atomicity
        await db.runTransaction(async (transaction) => {
          // Get current wallet data
          const walletDoc = await transaction.get(walletRef);
          const currentBalance = walletDoc.exists
            ? walletDoc.data().balance || 0
            : 0;
          const newBalance = currentBalance + amount;

          // Create transaction record matching the app's schema (except timestamp)
          const transactionData = {
            type: "credit",
            amount: amount,
            description: `Wallet funding via Stripe - $${amount.toFixed(2)}`,
            timestamp: Date.now(), // Use milliseconds since epoch to match app schema
            status: "completed",
            postId: null,
            paymentMethodId: paymentIntent.payment_method,
            externalTransactionId: paymentIntentId,
            metadata: {
              paymentIntentId: paymentIntentId,
              stripePaymentMethod: paymentIntent.payment_method,
              processedBy: "firebase-function",
            },
          };

          // Add transaction
          const newTransactionRef = transactionsRef.doc();
          transaction.set(newTransactionRef, transactionData);

          // Update wallet balance
          transaction.set(
            walletRef,
            {
              balance: newBalance,
              lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
            },
            { merge: true }
          );
        });

        return res.status(200).json({
          message: "Payment processed successfully",
          success: true,
          amount: amount,
          newBalance: null, // We don't return the balance for security
        });
      } catch (error) {
        console.error("Error processing payment completion:", error);
        return res.status(500).json({
          error: "Failed to process payment completion",
          success: false,
        });
      }
    });
  }
);
/**
 * Stripe Checkout Session Creation
 * 
 */
exports.createCheckoutSession = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      // ✅ CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-Requested-With, Accept, Origin",
        "Access-Control-Max-Age": "86400",
        "Content-Type": "application/json",
      });

      // ✅ Preflight
      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      // ✅ Only POST allowed
      if (req.method !== "POST") {
        return res.status(405).json({
          success: false,
          error: "Method not allowed. Use POST.",
        });
      }

      // ✅ Validate request body
      const { amount, currency = "usd", email, uid, successUrl, cancelUrl } = req.body || {};
      if (!amount || !email || !uid) {
        return res.status(400).json({
          success: false,
          error: "Missing required fields: amount, uid, and email.",
        });
      }

      // ✅ Create or find customer
      let customer;
      try {
        const existing = await stripe.customers.list({ email, limit: 1 });
        customer = existing.data[0]
          ? existing.data[0]
          : await stripe.customers.create({ email });
      } catch (err) {
        console.error("Customer error:", err);
        return res.status(500).json({
          success: false,
          error: "Failed to create/retrieve customer.",
        });
      }

      // ✅ Create Checkout Session
      let session;
      try {
        session = await stripe.checkout.sessions.create({
          mode: "payment",
          payment_method_types: ["card"],
          customer: customer.id,
          line_items: [
            {
              price_data: {
                currency,
                product_data: { name: "Money Mouthy Wallet ReUp!" }, 
                unit_amount: Math.round(amount), // cents
              },
              quantity: 1,
            },
          ],
          success_url: successUrl || "https://yourapp.com/success",
          cancel_url: cancelUrl || "https://yourapp.com/cancel",
          metadata: {
          uid,
          email,
          amount,
          },
        });
      } catch (err) {
        console.error("Checkout session error:", err);
        return res.status(500).json({
          success: false,
          error: "Failed to create Checkout Session.",
        });
      }

      // ✅ Return Checkout URL
      return res.status(200).json({
        success: true,
        checkoutUrl: session.url,
        sessionId: session.id,
        sessionFull: session,
      });
    } catch (err) {
      console.error("Unexpected error:", err);
      return res.status(500).json({
        success: false,
        error: "Unexpected error creating Checkout Session.",
      });
    }
  });
});

/*
Webhook to listen for payments and update wallet balances

*/
exports.handleStripeWebhook = functions.https.onRequest(async (req, res) => {
  const sig = req.headers["stripe-signature"];
  let event;

  try {
    const endpointSecret = 'whsec_n8gDCUGYL9dfQfkDjImYd7VHckFpbgew';
    event = stripe.webhooks.constructEvent(req.rawBody, sig, endpointSecret);
  } catch (err) {
    console.error("⚠️ Webhook signature verification failed:", err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  if (event.type === "checkout.session.completed") {
    const session = event.data.object;
    const uid = session.metadata.uid;
    const amount = parseInt(session.metadata.amount);

    console.log(`✅ Payment successful for UID: ${uid}, amount: ${amount}`);

    const db = admin.firestore();
    const walletRef = db.collection("wallets_new").doc(uid);

    // 1️⃣ Ensure wallet exists & increment balance atomically
    await walletRef.set(
      {
        userId: uid,
        balance: admin.firestore.FieldValue.increment(amount / 100.0), // 👈 store as dollars to match Dart Wallet entity
      },
      { merge: true }
    );

    // 2️⃣ Add transaction into flat transactions_new collection
    await db.collection("transactions_new").doc(Date.now().toString()).set({
      id: Date.now().toString(),
      userId: uid,
      amount: amount / 100.0, // store in dollars, since your repo expects `double`
      type: "deposit",
      description: "Wallet funding via Stripe",
      methodType: "Stripe",
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });
  }

  // Always respond quickly
  res.json({ received: true });
});

// ------------------------------------------------------------ Proxy Server

const axios = require("axios");


exports.proxyRequest = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    const proxyingUrl = req.query.proxyingUrl;

    if (!proxyingUrl || typeof proxyingUrl !== "string") {
      return res.status(400).json({ error: "Missing or invalid 'proxyingUrl' query param." });
    }

    try {
      const axiosResponse = await axios({
        url: proxyingUrl,
        method: req.method,
        // ⚠️ Filter out problematic headers
        headers: filterRequestHeaders(req.headers),
        responseType: "arraybuffer", // for images or binary data
        validateStatus: () => true,
      });

      // CORS
      res.set("Access-Control-Allow-Origin", "*");
      res.set("Access-Control-Allow-Headers", "*");
      res.set("Access-Control-Allow-Methods", "*");

      // Forward response headers (except forbidden ones)
      for (const [key, value] of Object.entries(axiosResponse.headers)) {
        try {
          if (isSafeHeader(key)) {
            res.setHeader(key, value);
          }
        } catch (_) {}
      }

      res.status(axiosResponse.status).send(Buffer.from(axiosResponse.data));
    } catch (error) {
      console.error("Proxy error:", error.message);
      res.status(500).json({
        error: "Proxy request failed",
        details: error.message,
      });
    }
  });
});

// 🔒 Filter out headers that can break TLS/hostnames
function filterRequestHeaders(originalHeaders) {
  const headers = { ...originalHeaders };
  delete headers.host;
  delete headers.referer;
  delete headers.origin;
  delete headers["x-forwarded-for"];
  delete headers["x-forwarded-host"];
  return headers;
}

// 🔒 Don't forward restricted response headers
function isSafeHeader(header) {
  const disallowed = ["transfer-encoding", "content-encoding", "connection"];
  return !disallowed.includes(header.toLowerCase());
}


// ------------------------------------------------------------------

// ======================== Auto Delete Media and Posts from their collections after 48 hours or certain time

/**
 * Scheduled function to delete posts and media older than 48 hours
 * Runs every hour using Firebase Cloud Scheduler
 */
exports.cleanupOldPostsAndMedia = functions.pubsub
  .schedule('0 * * * *') // Run every hour at minute 0
  .timeZone('UTC')
  .onRun(async (context) => {
    const db = admin.firestore();
    const storage = admin.storage().bucket();

    console.log('🧹 Starting cleanup of posts and media older than 48 hours...');

    try {
      // Calculate cutoff time (48 hours ago)
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - 48);
      const cutoffTimestamp = admin.firestore.Timestamp.fromDate(cutoffTime);

      console.log(`📅 Cutoff time: ${cutoffTime.toISOString()}`);

      // 1. Find old posts
      const oldPostsQuery = await db.collection('posts_new')
        .where('timestamp', '<', cutoffTimestamp)
        .get();

      console.log(`📝 Found ${oldPostsQuery.docs.length} old posts to delete`);

      if (oldPostsQuery.docs.length === 0) {
        console.log('✅ No old posts to clean up');
        return null;
      }

      // 2. Collect media URLs and post IDs
      const mediaUrls = [];
      const postIds = [];
      const pollIds = [];

      oldPostsQuery.docs.forEach(doc => {
        const post = doc.data();
        postIds.push(doc.id);

        // Collect image URLs
        if (post.imageUrls && Array.isArray(post.imageUrls)) {
          mediaUrls.push(...post.imageUrls);
        }

        // Collect video URL
        if (post.videoUrl) {
          mediaUrls.push(post.videoUrl);
        }

        // Collect poll ID for cleanup
        if (post.pollId) {
          pollIds.push(post.pollId);
        }
      });

      console.log(`🖼️ Found ${mediaUrls.length} media files to delete`);
      console.log(`📊 Found ${pollIds.length} polls to delete`);

      // 3. Delete media files from Firebase Storage
      const deletePromises = mediaUrls.map(async (url) => {
        try {
          // Extract file path from URL
          const urlParts = url.split('/o/')[1];
          if (urlParts) {
            const filePath = decodeURIComponent(urlParts.split('?')[0]);
            console.log(`🗑️ Deleting media file: ${filePath}`);
            await storage.file(filePath).delete();
          }
        } catch (error) {
          console.error(`❌ Error deleting media file ${url}:`, error);
        }
      });

      // 4. Delete posts from Firestore
      const postDeletePromises = postIds.map(async (postId) => {
        try {
          console.log(`🗑️ Deleting post: ${postId}`);
          await db.collection('posts_new').doc(postId).delete();
        } catch (error) {
          console.error(`❌ Error deleting post ${postId}:`, error);
        }
      });

      // 5. Delete associated polls
      const pollDeletePromises = pollIds.map(async (pollId) => {
        try {
          console.log(`🗑️ Deleting poll: ${pollId}`);
          await db.collection('polls').doc(pollId).delete();
        } catch (error) {
          console.error(`❌ Error deleting poll ${pollId}:`, error);
        }
      });

      // 6. Delete associated comments (they're embedded in posts, so they'll be deleted with posts)
      // Comments are stored as arrays within posts, so they'll be cleaned up automatically

      // Execute all deletions
      await Promise.all([
        ...deletePromises,
        ...postDeletePromises,
        ...pollDeletePromises
      ]);

      console.log(`✅ Cleanup completed successfully!`);
      console.log(`📊 Summary:`);
      console.log(`   - Posts deleted: ${postIds.length}`);
      console.log(`   - Media files deleted: ${mediaUrls.length}`);
      console.log(`   - Polls deleted: ${pollIds.length}`);

      return null;

    } catch (error) {
      console.error('❌ Error during cleanup:', error);
      throw new functions.https.HttpsError('internal', 'Cleanup failed', error);
    }
  });

/**
 * Manual cleanup function that can be called via HTTP for testing
 * Supports both authenticated and unauthenticated access for testing
 */
exports.manualCleanup = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      // Set CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "*",
      });

      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      // Allow both GET (for testing) and POST
      if (req.method !== "POST" && req.method !== "GET") {
        return res.status(405).json({
          success: false,
          error: "Method not allowed. Use POST or GET.",
        });
      }

      // // Simple API key authentication (optional)
      // const apiKey = req.headers['x-api-key'] || req.query.apiKey;
      // const expectedApiKey = functions.config().cleanup?.api_key;

      // if (expectedApiKey && apiKey !== expectedApiKey) {
      //   console.log('❌ Invalid or missing API key');
      //   return res.status(403).json({
      //     success: false,
      //     error: "Forbidden: Invalid API key",
      //   });
      // }

      // Get hours from request body or query params (default 48)
      let hours = 48;
      if (req.method === "POST" && req.body) {
        hours = req.body.hours || 48;
      } else if (req.method === "GET") {
        hours = parseInt(req.query.hours) || 48;
      }

      const db = admin.firestore();
      const storage = admin.storage().bucket();

      console.log(`🧹 Manual cleanup requested for posts older than ${hours} hours`);

      // Calculate cutoff time
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - hours);
      const cutoffTimestamp = admin.firestore.Timestamp.fromDate(cutoffTime);

      // Find old posts
      const oldPostsQuery = await db.collection('posts_new')
        .where('timestamp', '<', cutoffTimestamp)
        .get();

      const postIds = oldPostsQuery.docs.map(doc => doc.id);
      const mediaUrls = [];
      const pollIds = [];

      oldPostsQuery.docs.forEach(doc => {
        const post = doc.data();
        if (post.imageUrls && Array.isArray(post.imageUrls)) {
          mediaUrls.push(...post.imageUrls);
        }
        if (post.videoUrl) {
          mediaUrls.push(post.videoUrl);
        }
        if (post.pollId) {
          pollIds.push(post.pollId);
        }
      });

      // Delete everything
      const deletePromises = mediaUrls.map(url => {
        try {
          const urlParts = url.split('/o/')[1];
          if (urlParts) {
            const filePath = decodeURIComponent(urlParts.split('?')[0]);
            return storage.file(filePath).delete();
          }
        } catch (error) {
          console.error(`Error deleting media file ${url}:`, error);
        }
      });

      const postDeletePromises = postIds.map(postId =>
        db.collection('posts_new').doc(postId).delete()
      );

      const pollDeletePromises = pollIds.map(pollId =>
        db.collection('polls').doc(pollId).delete()
      );

      await Promise.all([
        ...deletePromises,
        ...postDeletePromises,
        ...pollDeletePromises
      ]);

      return res.status(200).json({
        success: true,
        message: `Cleanup completed successfully`,
        summary: {
          postsDeleted: postIds.length,
          mediaFilesDeleted: mediaUrls.length,
          pollsDeleted: pollIds.length,
          hoursThreshold: hours
        }
      });

    } catch (error) {
      console.error('Error during manual cleanup:', error);
      return res.status(500).json({
        success: false,
        error: 'Cleanup failed',
        details: error.message
      });
    }
  });
});