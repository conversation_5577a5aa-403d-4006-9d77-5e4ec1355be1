import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';
import 'package:moneymouthy/features/auth/presentation/pages/auth_page.dart';
import 'package:moneymouthy/features/auth/presentation/pages/login_page.dart';
import 'package:moneymouthy/features/auth/presentation/pages/register_page.dart';
import 'package:moneymouthy/features/home/<USER>/pages/home_page.dart';
import 'package:moneymouthy/features/onboard/pages/about_screen.dart';
import 'package:moneymouthy/features/onboard/pages/landing_page.dart';
import 'package:moneymouthy/features/onboard/pages/privacy_policy_page.dart';
import 'package:moneymouthy/features/onboard/pages/splash_screen.dart';
import 'package:moneymouthy/features/onboard/pages/support_page.dart';
import 'package:moneymouthy/features/onboard/pages/terms_of_service_page.dart';
import 'package:moneymouthy/features/profile/presentation/pages/profile_page.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:moneymouthy/features/wallet/presentation/pages/payment_cancel.dart';
import 'package:moneymouthy/features/wallet/presentation/pages/payment_success.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:moneymouthy/globals.dart';
import 'package:moneymouthy/routes/helpers/admin/admin_helper.dart';
import 'helpers/post/post_helper.dart';

/// Keys for navigation (good for nested navigation)
final _rootNavigatorKey = GlobalKey<NavigatorState>();

class AppRouter {
  static GoRouter router(BuildContext context) {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/splash',
      redirect: (context, state) {
        final authState = context.read<AuthCubit>().state;
        final currentPath = state.matchedLocation;
        final isWeb = kIsWeb;

        // Handle splash screen only
        if (currentPath == '/splash') {
          if (authState is! AuthLoading) {
            if (authState is Authenticated) {
              return '/home';
            } else {
              return isWeb ? '/landing' : '/auth';
            }
          }
          return null;
        }

        // For authenticated users, redirect away from auth pages only
        if (authState is Authenticated) {
          final authPages = ['/auth', '/login', '/signup'];
          if (authPages.contains(currentPath)) {
            return '/home';
          }
        }

        // For unauthenticated users, only protect /home
        if (authState is UnAuthenticated && currentPath == '/home') {
          return isWeb ? '/landing' : '/auth';
        }

        // Allow all other routes
        return null;
      },
      // REMOVED: refreshListenable to prevent constant rebuilds
      routes: [
        /// Splash Screen - Initial loading screen
        GoRoute(
          path: '/splash',
          name: 'splash',
          builder: (context, state) => const SplashScreen(),
        ),

        /// Landing Page - With navigation listener for web
        GoRoute(
          path: '/landing',
          name: 'landing',
          builder: (context, state) {
            if (kIsWeb) {
              return BlocListener<AuthCubit, AuthState>(
                listener: (context, authState) {
                  // Optional: Auto-navigate to home after auth on web
                  // Remove this if you want users to manually navigate
                  if (authState is Authenticated) {
                    // Uncomment the line below if you want auto-navigation to home
                    // context.go('/home');
                  }
                },
                child: const LandingPage(),
              );
            } else {
              // On mobile, redirect based on auth state
              final authState = context.read<AuthCubit>().state;
              if (authState is Authenticated) {
                return HomePage(key: homePageKey);
              } else {
                return const AuthPage();
              }
            }
          },
        ),

        /// Auth Page - With navigation listener
        GoRoute(
          path: '/auth',
          name: 'auth',
          builder: (context, state) => BlocListener<AuthCubit, AuthState>(
            listener: (context, authState) {
              if (authState is Authenticated) {
                context.go('/home');
              }
            },
            child: const AuthPage(),
          ),
        ),

        /// Sign Up Page - With navigation listener
        GoRoute(
          path: '/signup',
          name: 'signup',
          builder: (context, state) => BlocListener<AuthCubit, AuthState>(
            listener: (context, authState) {
              if (authState is Authenticated) {
                context.go('/home');
              }
            },
            child: const RegisterPage(),
          ),
        ),

        /// Login Page - With navigation listener
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => BlocListener<AuthCubit, AuthState>(
            listener: (context, authState) {
              if (authState is Authenticated) {
                context.go('/home');
              }
            },
            child: const LoginPage(),
          ),
        ),

        /// Home Page - Auth check in builder
        GoRoute(
          path: '/home',
          name: 'home',
          builder: (context, state) {
            return BlocBuilder<AuthCubit, AuthState>(
              builder: (context, authState) {
                if (authState is Authenticated) {
                  return HomePage(key: homePageKey);
                } else if (authState is AuthLoading) {
                  return const ConstrainedScaffold(
                    body: Center(child: LoadingIndicator()),
                  );
                } else {
                  // Redirect to appropriate auth page
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (context.mounted) {
                      context.go(kIsWeb ? '/landing' : '/auth');
                    }
                  });
                  return const ConstrainedScaffold(
                    body: Center(child: LoadingIndicator()),
                  );
                }
              },
            );
          },
        ),

        /// Profile Page - Always accessible (public)
        GoRoute(
          path: '/profile/:userId',
          name: 'profile',
          builder: (context, state) {
            final userId = state.pathParameters['userId']!;
            return ProfilePage(uid: userId);
          },
        ),

        /// Post Detail Page - Always accessible (public)
        GoRoute(
          path: '/post/:postId',
          name: 'postDetail',
          builder: (context, state) {
            final postId = state.pathParameters['postId']!;
            return PostDetailWrapper(postId: postId);
          },
        ),

        // =============================== INFO PAGES - Always accessible
        GoRoute(
          path: '/privacy',
          name: 'privacy',
          builder: (context, state) => const PrivacyPolicyPage(),
        ),

        GoRoute(
          path: '/terms',
          name: 'terms',
          builder: (context, state) => const TermsOfServicePage(),
        ),

        GoRoute(
          path: '/about',
          name: 'about',
          builder: (context, state) => const AboutScreen(),
        ),

        GoRoute(
          path: '/support',
          name: 'support',
          builder: (context, state) => const SupportPage(),
        ),

        // =============================== PAYMENT ROUTES - Always accessible
        GoRoute(
          path: '/success',
          name: 'success',
          builder: (context, state) {
            return PaymentSuccess(state: state);
          },
        ),

        GoRoute(
          path: '/cancel',
          name: 'cancel',
          builder: (context, state) => PaymentCancel(state: state),
        ),

        // =============================== ADMIN ROUTES - Always accessible only by Admins
        GoRoute(
          path: '/admin',
          name: 'admin',
          builder: (context, state) => const AdminHelper(),
        ),
      ],
    );
  }
}
