/* 
Post States
*/
import 'package:flutter/material.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:toastification/toastification.dart';

abstract class PostStates {}

// initial
class PostsInitial extends PostStates {}

// loading...
class PostsLoading extends PostStates {}

// uploading...
class PostsUploading extends PostStates {}

// uploading with progress
class PostsUploadingProgress extends PostStates {
  final String stage;
  final double progress;
  PostsUploadingProgress(this.stage, this.progress);
}

// loaded
class PostsLoaded extends PostStates {
  final List<Post> posts;
  PostsLoaded(this.posts);
}

// // success
// class PostsSuccess extends PostStates {
//   final String message;
//   PostsSuccess(this.message);
// }

// error
class PostsError extends PostStates {
  final String message;
  PostsError(this.message) {
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text('Error'),
      description: Text(message),
      type: ToastificationType.error,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: Duration(seconds: 3),
    );
  }
}
