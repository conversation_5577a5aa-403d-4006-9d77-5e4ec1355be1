import 'dart:typed_data';

abstract class StorageRepo {
  // upload Profile Images on Mobile Platforms
  Future<String?> uploadProfileImageMobile(String path, String fileName);

  // upload Profile Images on Web Platforms
  Future<String?> uploadProfileImageWeb(Uint8List fileBytes, String fileName);

  // upload Post Images on Mobile Platforms
  Future<String?> uploadPostImageMobile(String path, String fileName);

  // upload Post Images on Web Platforms
  Future<String?> uploadPostImageWeb(Uint8List fileBytes, String fileName);

  // upload Multiple Post Images on Mobile Platforms
  Future<List<String>> uploadMultiplePostImagesMobile(
    List<String> paths,
    String postId,
  );

  // upload Multiple Post Images on Web Platforms
  Future<List<String>> uploadMultiplePostImagesWeb(
    List<Uint8List> fileBytes,
    String postId,
  );

  // upload Post Videos on Mobile Platforms
  Future<String?> uploadPostVideoMobile(String path, String fileName);

  // upload Post Videos on Web Platforms
  Future<String?> uploadPostVideoWeb(Uint8List fileBytes, String fileName);
}
