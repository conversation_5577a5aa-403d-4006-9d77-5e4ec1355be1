/*
Admin Wallet Management States
*/

import 'package:moneymouthy/features/wallet/domain/entities/wallet.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart';

abstract class AdminWalletManagementState {}

class AdminWalletManagementInitial extends AdminWalletManagementState {}

class AdminWalletManagementLoading extends AdminWalletManagementState {}

class AdminWalletManagementWalletsLoaded extends AdminWalletManagementState {
  final List<Map<String, dynamic>> wallets;

  AdminWalletManagementWalletsLoaded({required this.wallets});
}

class AdminWalletManagementWalletSelected extends AdminWalletManagementState {
  final Wallet wallet;

  AdminWalletManagementWalletSelected({required this.wallet});
}

class AdminWalletManagementTransactionsLoaded
    extends AdminWalletManagementState {
  final List<Transaction> transactions;

  AdminWalletManagementTransactionsLoaded({required this.transactions});
}

class AdminWalletManagementAllTransactionsLoaded
    extends AdminWalletManagementState {
  final List<Map<String, dynamic>> transactions;

  AdminWalletManagementAllTransactionsLoaded({required this.transactions});
}

class AdminWalletManagementStatsLoaded extends AdminWalletManagementState {
  final Map<String, dynamic> stats;

  AdminWalletManagementStatsLoaded({required this.stats});
}


class AdminWalletManagementError extends AdminWalletManagementState {
  final String message;

  AdminWalletManagementError(this.message);
}
