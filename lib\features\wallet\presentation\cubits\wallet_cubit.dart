import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:moneymouthy/globals.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart';
import 'package:moneymouthy/features/wallet/domain/repos/payment_repo.dart';
import 'package:moneymouthy/features/wallet/domain/repos/wallet_repo.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_states.dart';

class WalletCubit extends Cubit<WalletStates> {
  final WalletRepo walletRepo;
  final PaymentRepo paymentRepo;

  final String baseUrl = appUrl();
  // current User
  final User? currentUser = FirebaseAuth.instance.currentUser;

  WalletCubit({required this.walletRepo, required this.paymentRepo})
    : super(WalletInitial());

  // Load wallet and transactions
  Future<void> loadWallet(String userId) async {
    try {
      emit(WalletLoading());
      final wallet = await walletRepo.getWallet(userId);
      final transactions = await walletRepo.getTransactions(userId);
      emit(WalletLoaded(wallet: wallet, transactions: transactions));
    } catch (e) {
      emit(WalletError("Failed to load wallet: $e"));
    }
  }

  // Add balance
  Future<void> addBalance(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      if (!kIsWeb) {
        // Process paymnet Throught Stripe
        final result = await paymentRepo.processPayment(amount);

        if (result) {
          await walletRepo.addBalance(
            userId,
            amount,
            description,
            methodType: 'Stripe',
          );
          await loadWallet(userId);
        }
      } else if (kIsWeb) {
        // Process Web throught Checkout
        await startCheckout(amount, currentUser?.email ?? '');
      }

      return;
    } catch (e) {
      emit(WalletError("Failed to add balance: $e"));
    }
  }

  Future<void> deductBalance(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      await walletRepo.deductBalance(
        userId,
        amount,
        description,

        'Manual Deduct',
      );
      await loadWallet(userId);
    } catch (e) {
      emit(WalletError("Failed to deduct balance: $e"));
    }
  }

  // Get transactions by type (optimized to avoid extra queries)
  Future<void> loadTransactionsByType(
    String userId,
    TransactionType type,
  ) async {
    try {
      emit(WalletLoading());
      final wallet = await walletRepo.getWallet(userId);
      final transactions = await walletRepo.getTransactionsByType(userId, type);
      emit(WalletLoaded(wallet: wallet, transactions: transactions));
    } catch (e) {
      emit(WalletError("Failed to load transactions: $e"));
    }
  }

  Future<void> startCheckout(double amount, String email) async {
    try {
      // Call Firebase Function
      final url = Uri.parse(
        "https://us-central1-money-mouthy.cloudfunctions.net/createCheckoutSession",
      );
      final body = jsonEncode({
        "amount": (amount * 100).round(),
        "currency": "usd",
        "email": email,
        "uid": currentUser?.uid,
        "successUrl": "$baseUrl/#/success",
        "cancelUrl": "$baseUrl/#/cancel",
      });
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: body,
      );
      final data = jsonDecode(response.body);
      final checkoutUrl = data['checkoutUrl'];

      if (checkoutUrl != null && await canLaunchUrl(Uri.parse(checkoutUrl))) {
        await launchUrl(
          Uri.parse(checkoutUrl),
          mode: LaunchMode.platformDefault,
        );
      } else {
        emit(WalletError("Could not launch Stripe Checkout"));
      }
    } catch (e) {
      debugPrint("Error starting checkout: $e");
      emit(WalletError('Failed to Process Web Payments $e'));
    }
  }

  Future<void> updateAllUsersBalanceWithCreation(
    double amount,
    String description, {
    String? methodType,
  }) async {
    try {
      await walletRepo.updateAllUsersBalanceWithCreation(
        amount,
        description,
        methodType: methodType,
      );
      await loadWallet(currentUser!.uid);
    } catch (e) {
      emit(WalletError("Failed to update all users balance: $e"));
    }
  }
}
