import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:moneymouthy/globals.dart';
import 'package:toastification/toastification.dart';

class PaymentSuccess extends StatelessWidget {
  // cotext and state
  final GoRouterState state;
  const PaymentSuccess({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    // Building a Nice UI that show s
    return ConstrainedScaffold(
      appBar: AppBar(
        title: const Text('Payment Success'),
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: () {
            // Check if user is authenticated before going to home
            final authState = context.read<AuthCubit>().state;
            if (authState is Authenticated) {
              context.go('/home');
            } else {
              context.go(kIsWeb ? '/landing' : '/auth');
            }
          },
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 80),
            const SizedBox(height: 16),
            const Text(
              'Payment Successful!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            // Text('${state.pathParameters}'),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                final authState = context.read<AuthCubit>().state;
                if (authState is Authenticated) {
                  context.go('/home');
                } else {
                  context.go(kIsWeb ? '/landing' : '/auth');
                }
              },
              child: const Text('Continue'),
            ),
          ],
        ),
      ),
    );
  }
}
