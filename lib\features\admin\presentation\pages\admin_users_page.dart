import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_user_management_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_user_management_state.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';

class AppUsersPage extends StatefulWidget {
  const AppUsersPage({super.key});

  @override
  State<AppUsersPage> createState() => _AppUsersPageState();
}

class _AppUsersPageState extends State<AppUsersPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  String _selectedFilter = 'all'; // Track selected filter

  @override
  void initState() {
    super.initState();
    // Load users when page initializes
    context.read<AdminUserManagementCubit>().loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      appBar: AppBar(
        title: const Text('User Management'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddUserDialog(context),
          ),
          // IconButton(
          //   icon: const Icon(Icons.admin_panel_settings),
          //   onPressed: () => _showAddAdminUserDialog(context),
          // ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search users by name or email...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              onChanged: (query) {
                context.read<AdminUserManagementCubit>().searchUsers(query);
              },
            ),
          ),

          // Filter buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                FilterChip(
                  selectedColor: Theme.of(context).colorScheme.tertiary,
                  label: const Text('All Users'),
                  selected: _selectedFilter == 'all',
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedFilter = 'all';
                      });
                      context.read<AdminUserManagementCubit>().loadUsers();
                    }
                  },
                ),
                const SizedBox(width: 8),
                FilterChip(
                  selectedColor: Theme.of(context).colorScheme.tertiary,
                  label: const Text('Blocked Users'),
                  selected: _selectedFilter == 'blocked',
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedFilter = 'blocked';
                      });
                      context
                          .read<AdminUserManagementCubit>()
                          .loadBlockedUsers();
                    }
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Users list
          Expanded(
            child:
                BlocBuilder<AdminUserManagementCubit, AdminUserManagementState>(
                  builder: (context, state) {
                    if (state is AdminUserManagementLoading) {
                      return Center(child: LoadingIndicator());
                    }

                    if (state is AdminUserManagementError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              state.message,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.error,
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                context
                                    .read<AdminUserManagementCubit>()
                                    .loadUsers();
                              },
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      );
                    }

                    if (state is AdminUserManagementLoaded) {
                      if (state.users.isEmpty) {
                        return const Center(child: Text('No users found'));
                      }

                      return ListView.builder(
                        controller: _scrollController,
                        itemCount: state.users.length,
                        itemBuilder: (context, index) {
                          final user = state.users[index];
                          return _buildUserCard(context, user);
                        },
                      );
                    }

                    return const Center(
                      child: Text('Welcome to User Management'),
                    );
                  },
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(BuildContext context, ProfileUser user) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // User cached image inside a container with ClipR`
                Container(
                  width: 48,
                  height: 48,
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary,
                    shape: BoxShape.circle,
                  ),
                  child: CachedNetworkImage(
                    imageUrl: user.profileImageUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => const LoadingIndicator(),
                    errorWidget: (context, url, error) => Icon(
                      Icons.person,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // User info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        user.email,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      if (user.bio.isNotEmpty)
                        Text(
                          user.bio,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                    ],
                  ),
                ),

                // Action buttons
                PopupMenuButton<String>(
                  onSelected: (value) =>
                      _handleUserAction(context, user, value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'view',
                      child: Text('View Details'),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Text('Edit Profile'),
                    ),
                    PopupMenuItem(
                      value: 'block',
                      child: Text(user.isBlocked == true ? 'Unblock' : 'Block'),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      textStyle: TextStyle(color: Colors.red),
                      child: Text('Delete User'),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // User stats
            Row(
              children: [
                _buildStatChip(
                  context,
                  Icons.article,
                  '${user.followers.length}',
                  'Followers',
                ),
                const SizedBox(width: 8),
                _buildStatChip(
                  context,
                  Icons.people,
                  '${user.following.length}',
                  'Following',
                ),
                const SizedBox(width: 8),
                _buildStatChip(
                  context,
                  Icons.category,
                  user.selectedPostCategory.name,
                  'Category',
                ),
                if (user.isBlocked == true) ...[
                  const SizedBox(width: 8),
                  Chip(
                    label: const Text('BLOCKED'),
                    backgroundColor: Colors.red.shade100,
                    labelStyle: TextStyle(color: Colors.red.shade800),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip(
    BuildContext context,
    IconData icon,
    String value,
    String label,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withAlpha(50),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 4),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  void _handleUserAction(
    BuildContext context,
    ProfileUser user,
    String action,
  ) {
    switch (action) {
      case 'view':
        _showUserDetailsDialog(context, user);
        break;
      case 'edit':
        _showEditUserDialog(context, user);
        break;
      case 'block':
        _toggleUserBlockStatus(context, user);
        break;
      case 'delete':
        _showDeleteUserDialog(context, user);
        break;
    }
  }

  void _showUserDetailsDialog(BuildContext context, ProfileUser user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('User Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Name', user.name),
              _buildDetailRow('Email', user.email),
              _buildDetailRow('Bio', user.bio),
              _buildDetailRow('Category', user.selectedPostCategory.name),
              _buildDetailRow('Post Amount', '\$${user.postAmount}'),
              _buildDetailRow('Followers', user.followers.length.toString()),
              _buildDetailRow('Following', user.following.length.toString()),
              _buildDetailRow(
                'Status',
                user.isBlocked == true ? 'Blocked' : 'Active',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showEditUserDialog(BuildContext context, ProfileUser user) {
    final nameController = TextEditingController(text: user.name);
    final bioController = TextEditingController(text: user.bio);
    final emailController = TextEditingController(text: user.email);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit User'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Name'),
              ),
              TextField(
                controller: emailController,
                decoration: const InputDecoration(labelText: 'Email'),
                enabled: false, // Email shouldn't be editable
              ),
              TextField(
                controller: bioController,
                decoration: const InputDecoration(labelText: 'Bio'),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final updates = {
                'name': nameController.text,
                'bio': bioController.text,
              };
              context.read<AdminUserManagementCubit>().updateUserProfile(
                user.uid,
                updates,
              );
              Navigator.of(context).pop();
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showAddUserDialog(BuildContext context) {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final bioController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New User'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Name *'),
              ),
              TextField(
                controller: emailController,
                decoration: const InputDecoration(labelText: 'Email *'),
                keyboardType: TextInputType.emailAddress,
              ),
              TextField(
                controller: bioController,
                decoration: const InputDecoration(labelText: 'Bio (optional)'),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty &&
                  emailController.text.isNotEmpty) {
                context.read<AdminUserManagementCubit>().createUser(
                  email: emailController.text,
                  name: nameController.text,
                  bio: bioController.text,
                );
                Navigator.of(context).pop();
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _toggleUserBlockStatus(BuildContext context, ProfileUser user) {
    final isBlocked = user.isBlocked == true;
    final action = isBlocked ? 'unblock' : 'block';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirm ${action.capitalize()}'),
        content: Text('Are you sure you want to $action ${user.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await context
                  .read<AdminUserManagementCubit>()
                  .toggleUserBlockStatus(user.uid, !isBlocked);

              // Reload the appropriate list based on current filter
              if (_selectedFilter == 'blocked') {
                context.read<AdminUserManagementCubit>().loadBlockedUsers();
              } else {
                context.read<AdminUserManagementCubit>().loadUsers();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isBlocked ? Colors.green : Colors.orange,
            ),
            child: Text(action.capitalize()),
          ),
        ],
      ),
    );
  }

  void _showDeleteUserDialog(BuildContext context, ProfileUser user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text(
          'Are you sure you want to delete ${user.name}? This action cannot be undone and will remove all their data including posts and wallet.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<AdminUserManagementCubit>().deleteUser(user.uid);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
