import 'package:flutter_bloc/flutter_bloc.dart';

class PostCostCubit extends Cubit<double> {
  PostCostCubit() : super(0.05); // Default minimum cost

  // Fixed cost - no dynamic calculation
  static const double baseCost = 0.05;

  // Set fixed cost from user's profile selection
  void setFixedCost(double cost) {
    final fixedCost = cost >= baseCost ? cost : baseCost;
    emit(double.parse(fixedCost.toStringAsFixed(3)));
  }

  void updateCost(double cost) {
    if (cost >= baseCost) {
      emit(cost);
    }
  }

  void resetCost() {
    emit(baseCost);
  }
}
