import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/post/presentation/components/video_player_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';

class LandingPage extends StatefulWidget {
  const LandingPage({super.key});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  late YoutubePlayerController _youtubeController;

  @override
  void initState() {
    super.initState();
    _youtubeController = YoutubePlayerController.fromVideoId(
      videoId: 'H9UEXTKkQ34',
      autoPlay: false,
      params: const YoutubePlayerParams(
        mute: false,
        showControls: true,
        showFullscreenButton: true,
        enableCaption: true,
        captionLanguage: 'en',
      ),
    );
  }

  @override
  void dispose() {
    _youtubeController.close();
    super.dispose();
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw Exception('Could not launch $url');
    }
  }

  void _navigateToSignup() {
    context.go('/signup');
  }

  void _navigateToLogin() {
    context.go('/login');
  }

  void _navigateToHome() {
    context.go('/home');
  }

  void _handleLogout() {
    context.read<AuthCubit>().logout();
  }

  @override
  Widget build(BuildContext context) {
    // If not on web, redirect to appropriate screen
    if (!kIsWeb) {
      return BlocBuilder<AuthCubit, AuthState>(
        builder: (context, authState) {
          if (authState is Authenticated) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.go('/home');
            });
          } else if (authState is! AuthLoading) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.go('/auth');
            });
          }
          return const Scaffold(body: Center(child: LoadingIndicator()));
        },
      );
    }

    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, authState) {
        final isLoggedIn = authState is Authenticated;
        final isLoading = authState is AuthLoading;

        return _buildScaffold(context, isLoggedIn, isLoading);
      },
    );
  }

  Widget _buildScaffold(BuildContext context, bool isLoggedIn, bool isLoading) {
    return Scaffold(
      backgroundColor: const Color(0xFF0f172a),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Hero Section
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: const [
                    Color.fromARGB(255, 0, 72, 255),
                    Color.fromARGB(255, 34, 57, 88),
                    Color.fromARGB(255, 39, 0, 137),
                  ],
                  transform: GradientRotation(
                    (DateTime.now().millisecondsSinceEpoch / 3000) %
                        (2 * 3.14159),
                  ),
                ),
              ),
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 80,
                  ),
                  child: Column(
                    children: [
                      // Logo and Title
                      Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color.fromARGB(255, 177, 175, 175),
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              'assets/images/money_mouth.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),
                      const Text(
                        'Money Mouthy',
                        style: TextStyle(
                          fontSize: 56,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: -1,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Put Your Money Where Your Mouth Is',
                        style: TextStyle(
                          fontSize: 24,
                          color: Colors.white70,
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Color(0xFF5159FF).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(50),
                          border: Border.all(
                            color: Color(0xFF5159FF).withOpacity(0.3),
                          ),
                        ),
                        child: const Text(
                          '💰 Your opinion has minimum value of \$0.05 or more! 💰',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 48),

                      // Action Buttons - Show different buttons based on auth state
                      if (isLoading) ...[
                        // Show loading state
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Checking authentication...',
                          style: TextStyle(color: Colors.white70, fontSize: 16),
                        ),
                      ] else if (isLoggedIn) ...[
                        // Show "Go to Home" for authenticated users
                        _buildPrimaryButton(
                          'Go to Home',
                          Icons.home,
                          _navigateToHome,
                        ),
                        const SizedBox(height: 16),
                        _buildSecondaryButton(
                          'Sign Out',
                          Icons.logout,
                          _handleLogout,
                        ),
                      ] else ...[
                        // Show Sign In/Sign Up for non-authenticated users
                        Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          alignment: WrapAlignment.center,
                          children: [
                            _buildPrimaryButton(
                              'Get Started',
                              Icons.rocket_launch,
                              _navigateToSignup,
                            ),
                            _buildSecondaryButton(
                              'Sign In',
                              Icons.login,
                              _navigateToLogin,
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Features Section
            Container(
              width: double.infinity,
              color: Colors.white,
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 40,
                  ),
                  child: Column(
                    children: [
                      // Download buttons
                      Wrap(
                        spacing: 16,
                        runSpacing: 16,
                        alignment: WrapAlignment.center,
                        children: [
                          _buildOutlinedDownloadButton(
                            'Download for Android',
                            Icons.android,
                            () => _launchURL(
                              'https://play.google.com/store/apps/details?id=com.moneymouthy',
                            ),
                          ),
                          _buildOutlinedDownloadButton(
                            'Download for iOS',
                            Icons.apple,
                            () => _launchURL('/downloads/moneymouthy-ios.ipa'),
                          ),
                        ],
                      ),

                      // YouTube Video Section
                      const SizedBox(height: 10),
                      VideoPlayerWidget(
                        videoUrl: "assets/videos/Money_Mouthy.mp4",
                        autoPlay: true,
                        showControls: true,
                        looping: true,
                      ),

                      const SizedBox(height: 5),
                      const Text(
                        'Why Money Mouthy?',
                        style: TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0f172a),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 48),

                      // Feature cards
                      Wrap(
                        spacing: 32,
                        runSpacing: 32,
                        alignment: WrapAlignment.center,
                        children: [
                          _buildFeatureCard(
                            Icons.monetization_on,
                            'Earn Money',
                            'Get paid for your opinions and posts',
                          ),
                          _buildFeatureCard(
                            Icons.people,
                            'Connect',
                            'Join a community of like-minded people',
                          ),
                          _buildFeatureCard(
                            Icons.trending_up,
                            'Grow',
                            'Build your following and influence',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Navigation Links Section
            Container(
              width: double.infinity,
              color: const Color(0xFF0f172a),
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 40,
                  ),
                  child: Wrap(
                    spacing: 24,
                    runSpacing: 16,
                    alignment: WrapAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () => context.go('/privacy'),
                        child: const Text(
                          'Privacy Policy',
                          style: TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () => context.go('/terms'),
                        child: const Text(
                          'Terms of Service',
                          style: TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () => context.go('/about'),
                        child: const Text(
                          'About Us',
                          style: TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () => context.go('/support'),
                        child: const Text(
                          'Support',
                          style: TextStyle(color: Colors.white70),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Footer
            Container(
              width: double.infinity,
              color: const Color(0xFF0f172a),
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 24),
              child: const Center(
                child: Text(
                  '© 2025 Money Mouthy. All rights reserved.',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrimaryButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF5159FF),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        elevation: 0,
      ),
    );
  }

  Widget _buildSecondaryButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        side: const BorderSide(color: Colors.white, width: 2),
      ),
    );
  }

  Widget _buildFeatureCard(IconData icon, String title, String description) {
    return Container(
      width: 300,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF5159FF).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: 32, color: const Color(0xFF5159FF)),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF0f172a),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOutlinedDownloadButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.black,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        side: const BorderSide(color: Colors.black, width: 1),
      ),
    );
  }
}
