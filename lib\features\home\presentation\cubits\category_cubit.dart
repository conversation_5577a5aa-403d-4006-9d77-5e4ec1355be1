import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';

class CategoryCubit extends Cubit<PostCategory?> {
  final CurrentUserProfileCubit currentUserProfileCubit;

  CategoryCubit({required this.currentUserProfileCubit}) : super(null);

  void selectCategory(PostCategory? category) {
    // Optimistic update
    emit(category);

    // Update profile in background
    if (category != null) {
      _updateUserProfile(category);
    }
  }

  void initializeFromProfile(PostCategory? profileCategory) {
    if (state != profileCategory) {
      emit(profileCategory);
    }
  }

  Future<void> _updateUserProfile(PostCategory category) async {
    try {
      // Get current profile state
      final profileState = currentUserProfileCubit.state;
      if (profileState is ProfileLoaded) {
        final currentUser = profileState.profileUser;

        // Update profile with new category
        await currentUserProfileCubit.updateCurrentUserProfile(
          uid: currentUser.uid,
          newSelectedPostCategory: category,
        );
      }
    } catch (e) {
      // Handle error silently or emit error state if needed
      // For now, keep the optimistic update
    }
  }

  void clearCategory() {
    emit(null);
  }
}
