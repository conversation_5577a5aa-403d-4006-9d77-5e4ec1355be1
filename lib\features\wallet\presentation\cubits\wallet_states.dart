import 'package:flutter/material.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart';
import 'package:moneymouthy/features/wallet/domain/entities/wallet.dart';
import 'package:toastification/toastification.dart';

abstract class WalletStates {}

class WalletInitial extends WalletStates {}

class WalletLoading extends WalletStates {}

class WalletLoaded extends WalletStates {
  final Wallet wallet;
  final List<Transaction> transactions;

  WalletLoaded({required this.wallet, required this.transactions});
}

class WalletError extends WalletStates {
  final String message;
  // show taost
  WalletError(this.message) {
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text('Wallet Error'),
      description: Text(message),
      type: ToastificationType.error,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: Duration(seconds: 3),
    );
  }

  // WalletError(this.message);
}
