import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';
import 'package:moneymouthy/features/post/presentation/components/post_tile.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_cubit.dart';
import 'package:moneymouthy/features/post/presentation/cubits/post_states.dart';
import 'package:moneymouthy/features/profile/presentation/components/bio_box.dart';
import 'package:moneymouthy/features/profile/presentation/components/follow_button.dart';
import 'package:moneymouthy/features/profile/presentation/components/profile_stats.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/pages/follower_page.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';

import '../../../auth/presentation/cubits/auth_cubit.dart';
import 'edit_profile_page.dart';

class ProfilePage extends StatefulWidget {
  final String uid;
  const ProfilePage({super.key, required this.uid});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late final authCubit = context.read<AuthCubit>();
  late final profileCubit = context.read<ProfileCubit>();

  // current user
  late AppUser? currentUser = authCubit.currentUser;

  // posts
  int postsCount = 0;

  // on startup
  @override
  void initState() {
    super.initState();
    profileCubit.fetchUserProfile(widget.uid);
  }

  // toggle follow
  void followButtonPressed() {
    final profileState = profileCubit.state;
    if (profileState is! ProfileLoaded) {
      return;
    }
    final profileUser = profileState.profileUser;
    final isFollowing = profileUser.followers.contains(currentUser!.uid);

    // optimistically update the UI
    if (isFollowing) {
      setState(() {
        profileUser.followers.remove(currentUser!.uid);
      });
    } else {
      setState(() {
        profileUser.followers.add(currentUser!.uid);
      });
    }

    // Update backend - both cubits handle their own optimistic updates
    profileCubit
        .toggleFollow(currentUser!.uid, widget.uid)
        .then((_) {
          // Also update current user profile cubit for sidebar/stats updates
          if (mounted) {
            context.read<CurrentUserProfileCubit>().toggleFollow(
              currentUser!.uid,
              widget.uid,
            );
          }
        })
        .catchError((e) {
          // rollback the UI on error
          if (mounted) {
            setState(() {
              if (isFollowing) {
                // restore follow
                profileUser.followers.add(currentUser!.uid);
              } else {
                // restore unfollow
                profileUser.followers.remove(currentUser!.uid);
              }
            });
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    // is Own Post
    bool isOwnPost = currentUser!.uid == widget.uid;
    return BlocBuilder<ProfileCubit, ProfileStates>(
      builder: (context, state) {
        // loaded
        if (state is ProfileLoaded) {
          // get loaded user
          final user = state.profileUser;

          return ConstrainedScaffold(
            appBar: AppBar(
              centerTitle: true,
              title: Text(user.name),
              foregroundColor: Theme.of(context).colorScheme.primary,
              actions: [
                if (isOwnPost)
                  IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditProfilePage(user: user),
                        ),
                      );
                    },
                    icon: Icon(Icons.edit),
                  ),
              ],
            ),
            body: ListView(
              children: [
                // email
                Center(
                  child: Text(
                    user.email,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                // profile picture
                Center(
                  child: ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: user.profileImageUrl,
                      height: 200,
                      width: 200,
                      fit: BoxFit.cover, // ✅ works properly here
                      placeholder: (context, url) =>
                          Center(child: LoadingIndicator()),
                      errorWidget: (context, url, error) => Icon(
                        Icons.person,
                        size: 72,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 25),

                // profile stats
                FutureBuilder<int>(
                  future: isOwnPost
                      ? context
                            .read<ProfileCubit>()
                            .getUnfollowingUsers(currentUser!.uid)
                            .then((users) => users.length)
                      : Future.value(0),
                  builder: (context, unfollowingSnapshot) {
                    final unfollowingCount = unfollowingSnapshot.data ?? 0;
                    return ProfileStats(
                      postCount: postsCount,
                      followerCount: user.followers.length,
                      followingCount: user.following.length,
                      unfollowingCount: unfollowingCount,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => FollowerPage(
                              followers: user.followers,
                              following: user.following,
                              currentUserId: isOwnPost
                                  ? currentUser!.uid
                                  : null,
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),

                // follow button
                if (!isOwnPost)
                  FollowButton(
                    onToggleFollow: followButtonPressed,
                    isFollowing: user.followers.contains(currentUser!.uid),
                  ),

                // bio Box
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Text(
                        "About me",
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                BioBox(text: user.bio),
                const SizedBox(height: 10),

                // list of posts from this user
                BlocBuilder<PostCubit, PostStates>(
                  builder: (context, state) {
                    if (state is PostsLoaded) {
                      final userPosts = state.posts
                          .where((p) => p.userId == widget.uid)
                          .toList();
                      postsCount = userPosts.length;
                      return Column(
                        children: [
                          if (userPosts.isNotEmpty)
                            Container(
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.outline,
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: Text(
                                "Posts",
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                ),
                              ),
                            ),
                          ListView.builder(
                            cacheExtent: 200,
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: userPosts.length,
                            itemBuilder: (context, index) {
                              // get post individually
                              final post = userPosts[index];
                              return PostTile(
                                post: post,
                                onDeletePressed: () => context
                                    .read<PostCubit>()
                                    .deletePost(post.id),
                              );
                            },
                          ),
                        ],
                      );
                    } else if (state is PostsLoading) {
                      return const Center(child: LoadingIndicator());
                    } else {
                      return const Text('No posts found...');
                    }
                  },
                ),
              ],
            ),
          );
        }
        // loading ...
        else if (state is ProfileLoading) {
          return Center(child: LoadingIndicator());
        } else if (state is ProfileError) {
          return Center(
            child: Text('Error loading profile...${state.message}'),
          );
        }
        return const Center(child: Text('No profile found...'));
      },
    );
  }
}
