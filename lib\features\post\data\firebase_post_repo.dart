import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:moneymouthy/features/post/domain/entities/comment.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/post/domain/repos/post_repo.dart';

class FirebasePostRepo implements PostRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;

  // store the posts in the collection called 'posts_new'
  final CollectionReference postsCollection = FirebaseFirestore.instance
      .collection('posts_new');

  @override
  Future<void> createPost(Post post) async {
    try {
      await postsCollection.doc(post.id).set(post.toJson());
    } catch (e) {
      throw Exception("Failed to create post: $e");
    }
  }

  @override
  Future<void> deletePost(String postId) async {
    await postsCollection.doc(postId).delete();
  }

  @override
  Future<Post?> getPostById(String postId) async {
    try {
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        return Post.fromJson(postDoc.data()! as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      throw Exception("Failed to get post: $e");
    }
  }

  @override
  Future<List<Post>> fetchAllPosts() async {
    try {
      final now = DateTime.now();
      final twentyFourHoursAgo = now.subtract(const Duration(hours: 24));

      // Query 1: Recent posts (within 24h) ordered by cost (descending), then timestamp (descending)
      final recentPostsQuery = await postsCollection
          .where(
            'timestamp',
            isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .orderBy('timestamp', descending: true)
          .get();

      // Query 2: Older posts (>24h) ordered by timestamp (descending)
      final olderPostsQuery = await postsCollection
          .where(
            'timestamp',
            isLessThanOrEqualTo: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .orderBy('timestamp', descending: true)
          .get();

      // Convert recent posts and sort by cost (Firebase can't sort by multiple fields with different priorities)
      List<Post> recentPosts = [];
      if (recentPostsQuery.docs.isNotEmpty) {
        recentPosts = recentPostsQuery.docs
            .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        // Manual sort: by cost (desc), then by timestamp (desc) for recent posts only
        recentPosts.sort((a, b) {
          final costComparison = b.postCost.compareTo(a.postCost);
          if (costComparison != 0) {
            return costComparison;
          }
          return b.timestamp.compareTo(a.timestamp);
        });
      }

      // Convert older posts (already sorted by timestamp from Firebase)
      List<Post> olderPosts = [];
      if (olderPostsQuery.docs.isNotEmpty) {
        olderPosts = olderPostsQuery.docs
            .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
        // No manual sorting needed - Firebase already sorted by timestamp desc
      }

      // Combine: recent posts first, then older posts
      return [...recentPosts, ...olderPosts];
    } catch (e) {
      throw Exception("Failed to fetch posts: $e");
    }
  }

  @override
  Future<List<Post>> fetchPostsByCategory(PostCategory category) async {
    try {
      final now = DateTime.now();
      final twentyFourHoursAgo = now.subtract(const Duration(hours: 24));

      // Query 1: Recent posts in category (within 24h) ordered by timestamp
      final recentPostsQuery = await postsCollection
          .where('category', isEqualTo: category.name)
          .where(
            'timestamp',
            isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .orderBy('timestamp', descending: true)
          .get();

      // Query 2: Older posts in category (>24h) ordered by timestamp
      final olderPostsQuery = await postsCollection
          .where('category', isEqualTo: category.name)
          .where(
            'timestamp',
            isLessThanOrEqualTo: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .orderBy('timestamp', descending: true)
          .get();

      // Convert recent posts and sort by cost
      List<Post> recentPosts = [];
      if (recentPostsQuery.docs.isNotEmpty) {
        recentPosts = recentPostsQuery.docs
            .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        // Manual sort: by cost (desc), then by timestamp (desc) for recent posts only
        recentPosts.sort((a, b) {
          final costComparison = b.postCost.compareTo(a.postCost);
          if (costComparison != 0) {
            return costComparison;
          }
          return b.timestamp.compareTo(a.timestamp);
        });
      }

      // Convert older posts (already sorted by timestamp from Firebase)
      List<Post> olderPosts = [];
      if (olderPostsQuery.docs.isNotEmpty) {
        olderPosts = olderPostsQuery.docs
            .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
      }

      // Combine: recent posts first, then older posts
      return [...recentPosts, ...olderPosts];
    } catch (e) {
      throw Exception("Failed to fetch posts by category: $e");
    }
  }

  @override
  Future<List<Post>> fetchAllPostsByUserId(String userId) async {
    try {
      final now = DateTime.now();
      final twentyFourHoursAgo = now.subtract(const Duration(hours: 24));

      // Query 1: Recent posts by user (within 24h)
      final recentPostsQuery = await postsCollection
          .where('userId', isEqualTo: userId)
          .where(
            'timestamp',
            isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .orderBy('timestamp', descending: true)
          .get();

      // Query 2: Older posts by user (>24h)
      final olderPostsQuery = await postsCollection
          .where('userId', isEqualTo: userId)
          .where(
            'timestamp',
            isLessThanOrEqualTo: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .orderBy('timestamp', descending: true)
          .get();

      // Convert recent posts and sort by cost
      List<Post> recentPosts = [];
      if (recentPostsQuery.docs.isNotEmpty) {
        recentPosts = recentPostsQuery.docs
            .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        // Manual sort: by cost (desc), then by timestamp (desc) for recent posts only
        recentPosts.sort((a, b) {
          final costComparison = b.postCost.compareTo(a.postCost);
          if (costComparison != 0) {
            return costComparison;
          }
          return b.timestamp.compareTo(a.timestamp);
        });
      }

      // Convert older posts (already sorted by timestamp from Firebase)
      List<Post> olderPosts = [];
      if (olderPostsQuery.docs.isNotEmpty) {
        olderPosts = olderPostsQuery.docs
            .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
      }

      // Combine: recent posts first, then older posts
      return [...recentPosts, ...olderPosts];
    } catch (e) {
      throw Exception("Failed to fetch posts: $e");
    }
  }

  /// Helper method to get posts with pagination support for better performance
  Future<List<Post>> fetchAllPostsWithPagination({
    DocumentSnapshot? lastDocument,
    int limit = 20,
  }) async {
    try {
      final now = DateTime.now();
      final twentyFourHoursAgo = now.subtract(const Duration(hours: 24));

      // Query recent posts with pagination
      Query recentQuery = postsCollection
          .where(
            'timestamp',
            isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .orderBy('timestamp', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        recentQuery = recentQuery.startAfterDocument(lastDocument);
      }

      final recentSnapshot = await recentQuery.get();

      List<Post> recentPosts = [];
      if (recentSnapshot.docs.isNotEmpty) {
        recentPosts = recentSnapshot.docs
            .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        // Sort recent posts by cost
        recentPosts.sort((a, b) {
          final costComparison = b.postCost.compareTo(a.postCost);
          if (costComparison != 0) {
            return costComparison;
          }
          return b.timestamp.compareTo(a.timestamp);
        });
      }

      // If we still need more posts and have space, fetch older posts
      List<Post> olderPosts = [];
      if (recentPosts.length < limit) {
        final remainingLimit = limit - recentPosts.length;

        final olderQuery = postsCollection
            .where(
              'timestamp',
              isLessThanOrEqualTo: Timestamp.fromDate(twentyFourHoursAgo),
            )
            .orderBy('timestamp', descending: true)
            .limit(remainingLimit);

        final olderSnapshot = await olderQuery.get();

        if (olderSnapshot.docs.isNotEmpty) {
          olderPosts = olderSnapshot.docs
              .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
              .toList();
        }
      }

      return [...recentPosts, ...olderPosts];
    } catch (e) {
      throw Exception("Failed to fetch posts with pagination: $e");
    }
  }

  @override
  Future<void> toggleLikePost(String postId, String userId) async {
    try {
      // get post document from firestore by post id
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        final post = Post.fromJson(postDoc.data()! as Map<String, dynamic>);
        // check if user has liked the post already
        final hasLiked = post.likes.contains(userId);
        if (hasLiked) {
          post.likes.remove(userId); // unlike
        } else {
          post.likes.add(userId); // like
        }
        // update likes in firestore with the new likes list
        await postsCollection.doc(postId).update({'likes': post.likes});
      } else {
        throw Exception("Post Not Found"); // post not found in firestore
      }
    } catch (e) {
      throw Exception("Failed to toggle like post: $e");
    }
  }

  @override
  Future<void> toggleHatePost(String postId, String userId) async {
    try {
      // get post document from firestore by post id
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        final post = Post.fromJson(postDoc.data()! as Map<String, dynamic>);
        // check if user has hated the post already
        final hasHated = post.hates.contains(userId);
        if (hasHated) {
          post.hates.remove(userId); // unhate
        } else {
          post.hates.add(userId); // hate
        }
        // update hates in firestore with the new hates list
        await postsCollection.doc(postId).update({'hates': post.hates});
      } else {
        throw Exception("Post Not Found"); // post not found in firestore
      }
    } catch (e) {
      throw Exception("Failed to toggle hate post: $e");
    }
  }

  @override
  Future<void> addComment(String postId, Comment comment) async {
    try {
      // get the post document from firestore
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        final post = Post.fromJson(postDoc.data()! as Map<String, dynamic>);
        post.comments.add(comment);
        // update comments in firestore with the new comments list
        await postsCollection.doc(postId).update({
          'comments': post.comments.map((comment) => comment.toJson()).toList(),
        });
      } else {
        throw Exception("Post Not Found"); // post not found in firestore
      }
    } catch (e) {
      throw Exception("Failed to add comment: $e");
    }
  }

  @override
  Future<void> deleteComment(String postId, String commentId) async {
    try {
      // get the post document from firestore
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        final post = Post.fromJson(postDoc.data()! as Map<String, dynamic>);
        post.comments.removeWhere((comment) => comment.id == commentId);
        // update comments in firestore with the new comments list
        await postsCollection.doc(postId).update({
          'comments': post.comments.map((comment) => comment.toJson()).toList(),
        });
      } else {
        throw Exception("Post Not Found"); // post not found in firestore
      }
    } catch (e) {
      throw Exception("Failed to delete comment: $e");
    }
  }

  //
}
