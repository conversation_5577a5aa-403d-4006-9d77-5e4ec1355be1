import 'package:cloud_firestore/cloud_firestore.dart';

enum TransactionType { deposit, spent }

class Transaction {
  final String id;
  final String userId;
  final double amount;
  final TransactionType type;
  final String description;
  final String methodType;
  final DateTime timestamp;

  Transaction({
    required this.id,
    required this.userId,
    required this.amount,
    required this.type,
    required this.description,
    required this.methodType,
    required this.timestamp,
  });

  // Convert transaction to Json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'amount': amount,
      'type': type.name,
      'description': description,
      'methodType': methodType,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }

  // Convert Json to transaction
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TransactionType.deposit,
      ),
      description: json['description'] ?? '',
      methodType: json['methodType'] ?? '',
      timestamp: json['timestamp'] != null
          ? (json['timestamp'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }
}
