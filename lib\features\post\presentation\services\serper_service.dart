import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:moneymouthy/constants/constants.dart';

class SerperService {
  final String _apiKey = '03ddc6608d3ce2ec6ec20ada5cce1e4e80177b2a';
  final String _baseUrl = 'https://google.serper.dev/images';

  Future<List<String>> searchImages(String query) async {
    if (_apiKey.isEmpty) {
      throw Exception('Serper API key not found');
    }

    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'X-API-KEY': _apiKey, 'Content-Type': 'application/json'},
        body: json.encode({
          'q': query,
          'num': 50,
          'type': 'images',
          'location': 'United States',
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final images = data['images'] as List<dynamic>;
        return images
            .map((image) => kProxyUrl + (image['imageUrl']?.toString() ?? ''))
            .where((url) => url.isNotEmpty)
            .take(50)
            .toList();
      } else {
        throw Exception('Failed to fetch images: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error searching images: $e');
      throw Exception('Failed to fetch images: $e');
    }
  }

  Future<Uint8List?> downloadImage(String url) async {
    try {
      debugPrint('Downloading image from: $url');

      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            },
          )
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              debugPrint('Image download timeout for: $url');
              throw Exception('Download timeout');
            },
          );

      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        if (bytes.isNotEmpty) {
          debugPrint('Successfully downloaded image: ${bytes.length} bytes');
          return bytes;
        } else {
          debugPrint('Downloaded image is empty: $url');
          return null;
        }
      } else {
        debugPrint('Failed to download image: ${response.statusCode} - $url');
        return null;
      }
    } catch (e) {
      debugPrint('Error downloading image from $url: $e');
      return null;
    }
  }
}
