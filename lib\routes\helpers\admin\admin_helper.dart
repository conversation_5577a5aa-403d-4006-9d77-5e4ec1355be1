import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_state.dart';
import 'package:moneymouthy/features/admin/presentation/pages/admin_page.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';
import 'package:moneymouthy/features/auth/presentation/pages/auth_page.dart';

class AdminHelper extends StatefulWidget {
  const AdminHelper({super.key});

  @override
  State<AdminHelper> createState() => _AdminHelperState();
}

class _AdminHelperState extends State<AdminHelper> {
  AdminCubit? adminCubit;
  bool hasInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!hasInitialized) {
      adminCubit = context.read<AdminCubit>();
      hasInitialized = true;
      _checkAdminStatus();
    }
  }

  Future<void> _checkAdminStatus() async {
    if (adminCubit != null) {
      await adminCubit!.verifyAdmin();
    }
  }

  Widget _buildAccessDenied() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.admin_panel_settings,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text('Admin access required'),
            const SizedBox(height: 8),
            const Text(
              'You do not have admin privileges',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go to Home'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoading(String message) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const LoadingIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      builder: (context, authState) {
        // Handle authentication states first
        if (authState is UnAuthenticated || authState is AuthError) {
          return const AuthPage();
        } else if (authState is AuthLoading || authState is AuthInitial) {
          return _buildLoading('Checking authentication...');
        } else if (authState is Authenticated) {
          // User is authenticated, now check admin status
          if (!hasInitialized) {
            return _buildLoading('Initializing admin check...');
          }

          return BlocBuilder<AdminCubit, AdminState>(
            builder: (context, adminState) {
              if (adminState is AdminLoading || adminState is AdminInitial) {
                return _buildLoading('Checking admin access...');
              } else if (adminState is AdminVerified) {
                // Admin is verified, show admin panel
                return AdminPage(currentAdmin: adminState.user);
              } else if (adminState is AdminNotVerified) {
                return _buildAccessDenied();
              } else if (adminState is AdminError) {
                return _buildAccessDenied();
              } else {
                return _buildAccessDenied();
              }
            },
          );
        } else {
          return const AuthPage();
        }
      },
      listener: (context, authState) {
        // Re-check admin status when auth state changes
        if (authState is Authenticated && hasInitialized) {
          _checkAdminStatus();
        }
      },
    );
  }
}
