import 'package:flutter/material.dart';

ThemeData darkMode = ThemeData(
  colorScheme: const ColorScheme.dark(
    // very dark - app bar + drawer color
    surface: Color.fromARGB(255, 9, 9, 9),

    // slightly light
    primary: Color.fromARGB(255, 221, 221, 221),

    // dark
    secondary: Colors.white,

    // slightly dark
    tertiary: Color(0xFF5F66FF),

    // very light
    inversePrimary: Color.fromARGB(255, 195, 195, 195),
    outline: Color.fromARGB(255, 195, 195, 195),

    // Surface tint
    surfaceTint: Colors.blue,
  ),
  scaffoldBackgroundColor: const Color.fromARGB(255, 9, 9, 9),
);
