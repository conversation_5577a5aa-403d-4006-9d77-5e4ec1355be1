import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';

/// Profile Repository

abstract class ProfileRepo {
  Future<ProfileUser?> fetchUserProfile(String uid);
  Future<void> updateProfile(ProfileUser updatedProfile);

  // toggle follow
  Future<void> toggleFollow(String currentUid, String targetUid);

  // get unfollowing users (users not followed by current user)
  Future<List<ProfileUser>> getUnfollowingUsers(String currentUid);
  // make all users follow each other (mutual following)
  Future<void> makeAllUsersMutuallyFollow();
  // make a user follow all other users they're not already following
  Future<void> makeUserFollowAllRemaining(String currentUid);
  // optimized version of makeUserFollowAllRemaining
  Future<void> makeUserFollowAllRemainingOptimized(String currentUid);
}
