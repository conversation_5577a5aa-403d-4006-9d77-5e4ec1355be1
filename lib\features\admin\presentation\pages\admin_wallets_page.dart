import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_wallet_management_cubit.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_wallet_management_state.dart';
import 'package:moneymouthy/features/wallet/domain/entities/wallet.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:toastification/toastification.dart';

class AdminWalletsPage extends StatefulWidget {
  const AdminWalletsPage({super.key});

  @override
  State<AdminWalletsPage> createState() => _AdminWalletsPageState();
}

class _AdminWalletsPageState extends State<AdminWalletsPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Load wallets when page initializes
    context.read<AdminWalletManagementCubit>().loadAllUserWallets();

    // Add listener to load data when tab changes
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) return;

      switch (_tabController.index) {
        case 0:
          // All Wallets tab
          context.read<AdminWalletManagementCubit>().loadAllUserWallets();
          break;
        case 1:
          // Transactions tab
          context.read<AdminWalletManagementCubit>().getAllTransactions();
          break;
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      appBar: AppBar(
        title: const Text('Wallet Management'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.group_add),
            onPressed: () => _showBulkOperationsDialog(context),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All Wallets', icon: Icon(Icons.account_balance_wallet)),
            Tab(text: 'Transactions', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildWalletsTab(), _buildTransactionsTab()],
      ),
    );
  }

  Widget _buildWalletsTab() {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search by name, email, or user ID...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surface,
            ),
            onChanged: (query) {
              setState(() {
                // The search will be handled in the BlocBuilder below
              });
            },
          ),
        ),

        // Wallets list
        Expanded(
          child:
              BlocBuilder<
                AdminWalletManagementCubit,
                AdminWalletManagementState
              >(
                builder: (context, state) {
                  if (state is AdminWalletManagementLoading) {
                    return const Center(child: LoadingIndicator());
                  }

                  if (state is AdminWalletManagementError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            state.message,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.error,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => context
                                .read<AdminWalletManagementCubit>()
                                .loadAllUserWallets(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  if (state is AdminWalletManagementWalletsLoaded) {
                    if (state.wallets.isEmpty) {
                      return const Center(child: Text('No wallets found'));
                    }

                    // Filter wallets based on search query
                    final filteredWallets = _searchController.text.isEmpty
                        ? state.wallets
                        : state.wallets.where((wallet) {
                            final query = _searchController.text.toLowerCase();
                            final name = (wallet['name'] ?? '').toLowerCase();
                            final email = (wallet['email'] ?? '').toLowerCase();
                            final userId = (wallet['userId'] ?? '')
                                .toLowerCase();

                            return name.contains(query) ||
                                email.contains(query) ||
                                userId.contains(query);
                          }).toList();

                    if (filteredWallets.isEmpty) {
                      return const Center(
                        child: Text('No wallets match your search'),
                      );
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      itemCount: filteredWallets.length,
                      itemBuilder: (context, index) {
                        final wallet = filteredWallets[index];
                        return _buildWalletCard(context, wallet);
                      },
                    );
                  }

                  return const Center(
                    child: Text('Welcome to Wallet Management'),
                  );
                },
              ),
        ),
      ],
    );
  }

  Widget _buildTransactionsTab() {
    return BlocBuilder<AdminWalletManagementCubit, AdminWalletManagementState>(
      builder: (context, state) {
        if (state is AdminWalletManagementLoading) {
          return const Center(child: LoadingIndicator());
        }

        if (state is AdminWalletManagementError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  state.message,
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context
                      .read<AdminWalletManagementCubit>()
                      .getAllTransactions(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is AdminWalletManagementAllTransactionsLoaded) {
          if (state.transactions.isEmpty) {
            return const Center(child: Text('No transactions found'));
          }

          return ListView.builder(
            itemCount: state.transactions.length,
            itemBuilder: (context, index) {
              final transaction = state.transactions[index];
              return _buildTransactionCard(context, transaction);
            },
          );
        }

        // Loading state for initial load
        return const Center(child: LoadingIndicator());
      },
    );
  }

  Widget _buildWalletCard(BuildContext context, Map<String, dynamic> wallet) {
    final userId = wallet['userId'] ?? '';
    final name = wallet['name'] ?? 'Unknown';
    final email = wallet['email'] ?? '';
    final balance = wallet['balance'] ?? 0.0;
    final profileImageUrl = wallet['profileImageUrl'] ?? '';

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // User profile image or wallet icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).colorScheme.outline.withAlpha(100),
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: profileImageUrl.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: profileImageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey.shade200,
                              child: const Icon(
                                Icons.person,
                                color: Colors.grey,
                                size: 24,
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey.shade200,
                              child: const Icon(
                                Icons.person,
                                color: Colors.grey,
                                size: 24,
                              ),
                            ),
                          )
                        : Container(
                            color: Colors.green.shade100,
                            child: Icon(
                              Icons.account_balance_wallet,
                              color: Colors.green.shade800,
                              size: 24,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 12),

                // Wallet info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        email,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        'ID: $userId',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        '\$${balance.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: Colors.green.shade700,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
                ),

                // Action buttons
                PopupMenuButton<String>(
                  onSelected: (value) =>
                      _handleWalletAction(context, wallet, value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'add',
                      child: Text('Add Balance'),
                    ),
                    const PopupMenuItem(
                      value: 'deduct',
                      child: Text('Deduct Balance'),
                    ),
                    const PopupMenuItem(
                      value: 'history',
                      child: Text('Transaction History'),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionCard(
    BuildContext context,
    Map<String, dynamic> transaction,
  ) {
    final amount = transaction['amount'] ?? 0.0;
    final type = transaction['type'] ?? 'deposit';
    final description = transaction['description'] ?? '';
    final timestamp = transaction['timestamp'] as DateTime?;
    final userId = transaction['userId'] ?? '';
    final userName = transaction['userName'] ?? 'Unknown';
    final userEmail = transaction['userEmail'] ?? '';
    final methodType = transaction['methodType'] ?? '';

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(50),
        ),
      ),
      child: Row(
        children: [
          // Transaction type icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: type == 'deposit'
                  ? Colors.green.withAlpha(25)
                  : Colors.red.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              type == 'deposit' ? Icons.add : Icons.remove,
              color: type == 'deposit' ? Colors.green : Colors.red,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // Transaction details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  description.isNotEmpty ? description : 'Transaction',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 2),
                Text(
                  methodType.isNotEmpty ? methodType : 'Wallet Transaction',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  userName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(150),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  userEmail,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(150),
                  ),
                ),
                const SizedBox(height: 2),
                if (timestamp != null)
                  Text(
                    _formatDateTime(timestamp),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withAlpha(150),
                    ),
                  ),
              ],
            ),
          ),

          // Amount
          Text(
            '${type == 'deposit' ? '+' : '-'}\$${amount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: type == 'deposit' ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  void _handleWalletAction(
    BuildContext context,
    Map<String, dynamic> wallet,
    String action,
  ) {
    final userId = wallet['userId'] ?? '';
    final name = wallet['name'] ?? 'Unknown';

    switch (action) {
      case 'add':
        _showAddBalanceDialog(context, userId, name);
        break;
      case 'deduct':
        _showDeductBalanceDialog(context, userId, name);
        break;
      case 'history':
        _showWalletTransactionHistory(context, userId);
        break;
    }
  }

  void _showAddBalanceDialog(
    BuildContext context,
    String userId,
    String userName,
  ) {
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Add Balance to $userName'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount *',
                  prefixText: '\$',
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  hintText: 'Reason for adding balance',
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final amount = double.tryParse(amountController.text) ?? 0.0;
              final description = descriptionController.text;

              if (amount > 0 && description.isNotEmpty) {
                context
                    .read<AdminWalletManagementCubit>()
                    .addBalanceToUserWallet(userId, amount, description);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Add Balance'),
          ),
        ],
      ),
    );
  }

  void _showDeductBalanceDialog(
    BuildContext context,
    String userId,
    String userName,
  ) {
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Deduct Balance from $userName'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount *',
                  prefixText: '\$',
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  hintText: 'Reason for deducting balance',
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final amount = double.tryParse(amountController.text) ?? 0.0;
              final description = descriptionController.text;

              if (amount > 0 && description.isNotEmpty) {
                context
                    .read<AdminWalletManagementCubit>()
                    .deductBalanceFromUserWallet(userId, amount, description);
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Deduct Balance'),
          ),
        ],
      ),
    );
  }

  void _showWalletTransactionHistory(BuildContext context, String userId) {
    context.read<AdminWalletManagementCubit>().getUserTransactionHistory(
      userId,
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Transaction History - User $userId'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child:
              BlocBuilder<
                AdminWalletManagementCubit,
                AdminWalletManagementState
              >(
                builder: (context, state) {
                  if (state is AdminWalletManagementLoading) {
                    return const Center(child: LoadingIndicator());
                  }

                  if (state is AdminWalletManagementTransactionsLoaded) {
                    if (state.transactions.isEmpty) {
                      return const Center(child: Text('No transactions found'));
                    }

                    return ListView.builder(
                      itemCount: state.transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = state.transactions[index];
                        return ListTile(
                          leading: Icon(
                            transaction.type == TransactionType.deposit
                                ? Icons.add_circle
                                : Icons.remove_circle,
                            color: transaction.type == TransactionType.deposit
                                ? Colors.green
                                : Colors.red,
                          ),
                          title: Text(transaction.description),
                          subtitle: Text(
                            _formatDateTime(transaction.timestamp),
                          ),
                          trailing: Text(
                            '${transaction.type == TransactionType.deposit ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: transaction.type == TransactionType.deposit
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      },
                    );
                  }

                  return const Center(
                    child: Text('Failed to load transaction history'),
                  );
                },
              ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showBulkOperationsDialog(BuildContext context) {
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bulk Add Balance'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'This will add the specified amount to ALL user wallets.',
                style: TextStyle(color: Colors.orange),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount per user *',
                  prefixText: '\$',
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  hintText: 'Reason for bulk balance addition',
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final amount = double.tryParse(amountController.text) ?? 0.0;
              final description = descriptionController.text;

              if (amount > 0 && description.isNotEmpty) {
                context
                    .read<AdminWalletManagementCubit>()
                    .bulkAddBalanceToAllUsers(amount, description);
                Navigator.of(context).pop();
              } else {
                //show toastification
                toastification.show(
                  alignment: Alignment.topCenter,
                  title: Text('Wallet Error'),
                  description: Text('Please fill in all fields correctly.'),
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  autoCloseDuration: Duration(seconds: 3),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Add to All Users'),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
