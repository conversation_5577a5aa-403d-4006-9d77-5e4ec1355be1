/*
Admin Wallet Management Repository
Handles wallet operations for admin panel
*/

import 'package:moneymouthy/features/wallet/domain/entities/wallet.dart';
import 'package:moneymouthy/features/wallet/domain/entities/transaction.dart'
    as wallet_entity;

abstract class AdminWalletManagementRepo {
  // Get all user wallets with user details
  Future<List<Map<String, dynamic>>> getAllUserWallets();

  // Get wallet by user ID
  Future<Wallet?> getWalletByUserId(String userId);

  // Add balance to user wallet
  Future<void> addBalanceToUserWallet(
    String userId,
    double amount,
    String description,
  );

  // Deduct balance from user wallet
  Future<void> deductBalanceFromUserWallet(
    String userId,
    double amount,
    String description,
  );

  // Get user transaction history
  Future<List<wallet_entity.Transaction>> getUserTransactionHistory(
    String userId,
  );

  // Get all transactions (admin view)
  Future<List<Map<String, dynamic>>> getAllTransactions();

  // Get wallet statistics
  Future<Map<String, dynamic>> getWalletStatistics();

  // Bulk add balance to all users
  Future<void> bulkAddBalanceToAllUsers(double amount, String description);
}
