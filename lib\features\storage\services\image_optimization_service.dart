import 'dart:io';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class ImageOptimizationService {
  // More aggressive optimization for mobile
  static const int maxWidth = 1024; // Reduced from 1280
  static const int maxHeight = 576; // Reduced from 720
  static const int maxFileSize = 800 * 1024; // 800KB (reduced from 1MB)
  static const int compressionQuality = 60; // Slightly reduced quality
  static const int smallFileThreshold = 200 * 1024; // 200KB

  /// Optimize image file for mobile platforms with better performance
  static Future<File?> optimizeImageFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('File does not exist: $filePath');
        return null;
      }

      final fileSize = await file.length();
      debugPrint(
        'Original file size: ${(fileSize / 1024).toStringAsFixed(2)}KB',
      );

      // Skip optimization for already small files
      if (fileSize <= smallFileThreshold) {
        debugPrint('File already small, skipping optimization');
        return file;
      }

      // Read and optimize
      final bytes = await file.readAsBytes();
      final optimizedBytes = await _optimizeImageBytesQuick(bytes);

      if (optimizedBytes != null && optimizedBytes.length < fileSize) {
        // Create optimized file with .jpg extension for better compression
        final optimizedPath = filePath.replaceAll(
          RegExp(r'\.[^.]+$'),
          '_opt.jpg',
        );
        final optimizedFile = File(optimizedPath);
        await optimizedFile.writeAsBytes(optimizedBytes);

        final optimizedSize = optimizedBytes.length;
        debugPrint(
          'Optimized file size: ${(optimizedSize / 1024).toStringAsFixed(2)}KB',
        );
        debugPrint(
          'Reduction: ${((1 - optimizedSize / fileSize) * 100).toStringAsFixed(1)}%',
        );

        return optimizedFile;
      }

      return file;
    } catch (e) {
      debugPrint('Error optimizing image file: $e');
      return null;
    }
  }

  /// Optimize image bytes for web platforms
  static Future<Uint8List?> optimizeImageBytes(Uint8List bytes) async {
    try {
      final originalSize = bytes.length;
      debugPrint(
        'Original bytes size: ${(originalSize / 1024).toStringAsFixed(2)}KB',
      );

      // Skip optimization for already small files
      if (originalSize <= smallFileThreshold) {
        debugPrint('Bytes already small, skipping optimization');
        return bytes;
      }

      final optimizedBytes = await _optimizeImageBytesQuick(bytes);

      if (optimizedBytes != null && optimizedBytes.length < originalSize) {
        final optimizedSize = optimizedBytes.length;
        debugPrint(
          'Optimized bytes size: ${(optimizedSize / 1024).toStringAsFixed(2)}KB',
        );
        debugPrint(
          'Reduction: ${((1 - optimizedSize / originalSize) * 100).toStringAsFixed(1)}%',
        );
        return optimizedBytes;
      }

      return bytes;
    } catch (e) {
      debugPrint('Error optimizing image bytes: $e');
      return null;
    }
  }

  /// Quick optimization method for better performance
  static Future<Uint8List?> _optimizeImageBytesQuick(Uint8List bytes) async {
    try {
      // Decode image
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      final image = frame.image;

      // Calculate new dimensions
      final originalWidth = image.width;
      final originalHeight = image.height;

      // More aggressive scaling for faster uploads
      double scale = 1.0;
      if (originalWidth > maxWidth || originalHeight > maxHeight) {
        final scaleWidth = maxWidth / originalWidth;
        final scaleHeight = maxHeight / originalHeight;
        scale = scaleWidth < scaleHeight ? scaleWidth : scaleHeight;
      }

      // Additional size reduction if file is very large
      final fileSize = bytes.length;
      if (fileSize > maxFileSize * 2) {
        scale *= 0.7; // Reduce by additional 30% for very large files
      }

      final newWidth = (originalWidth * scale).round();
      final newHeight = (originalHeight * scale).round();

      if (scale >= 0.95 && fileSize <= maxFileSize) {
        // No significant resize needed and file size is acceptable
        return bytes;
      }

      debugPrint(
        'Resizing from ${originalWidth}x$originalHeight to ${newWidth}x$newHeight',
      );

      // Create recorder and canvas for resizing
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // Use lower quality for faster processing on mobile
      final paint = Paint()
        ..filterQuality = kIsWeb ? FilterQuality.high : FilterQuality.medium;

      canvas.drawImageRect(
        image,
        Rect.fromLTWH(
          0,
          0,
          originalWidth.toDouble(),
          originalHeight.toDouble(),
        ),
        Rect.fromLTWH(0, 0, newWidth.toDouble(), newHeight.toDouble()),
        paint,
      );

      // Convert to image
      final picture = recorder.endRecording();
      final resizedImage = await picture.toImage(newWidth, newHeight);

      // Use JPEG format for better compression
      final byteData = await resizedImage.toByteData(
        format: ui.ImageByteFormat.png, // Still use PNG for quality
      );

      if (byteData != null) {
        return byteData.buffer.asUint8List();
      }

      return null;
    } catch (e) {
      debugPrint('Error in _optimizeImageBytesQuick: $e');
      return null;
    }
  }

  /// Optimize multiple image files with better performance
  static Future<List<File>> optimizeMultipleImageFiles(
    List<String> filePaths,
  ) async {
    if (filePaths.isEmpty) return [];

    debugPrint('Starting optimization of ${filePaths.length} images');

    // Process in smaller batches to avoid memory issues
    const int batchSize = 2;
    final List<File> optimizedFiles = [];

    for (int i = 0; i < filePaths.length; i += batchSize) {
      final end = (i + batchSize < filePaths.length)
          ? i + batchSize
          : filePaths.length;
      final batch = filePaths.sublist(i, end);

      debugPrint(
        'Processing batch ${(i / batchSize) + 1}/${((filePaths.length - 1) / batchSize).floor() + 1}',
      );

      final batchTasks = batch.map((path) => optimizeImageFile(path));
      final batchResults = await Future.wait(batchTasks);

      for (var result in batchResults) {
        if (result != null) {
          optimizedFiles.add(result);
        }
      }
    }

    debugPrint(
      'Completed optimization: ${optimizedFiles.length}/${filePaths.length} images optimized',
    );
    return optimizedFiles;
  }

  /// Optimize multiple image bytes with better performance
  static Future<List<Uint8List>> optimizeMultipleImageBytes(
    List<Uint8List> bytesList,
  ) async {
    if (bytesList.isEmpty) return [];

    debugPrint('Starting optimization of ${bytesList.length} image bytes');

    // Process in smaller batches
    const int batchSize = 2;
    final List<Uint8List> optimizedBytesList = [];

    for (int i = 0; i < bytesList.length; i += batchSize) {
      final end = (i + batchSize < bytesList.length)
          ? i + batchSize
          : bytesList.length;
      final batch = bytesList.sublist(i, end);

      debugPrint(
        'Processing batch ${(i / batchSize) + 1}/${((bytesList.length - 1) / batchSize).floor() + 1}',
      );

      final batchTasks = batch.map((bytes) => optimizeImageBytes(bytes));
      final batchResults = await Future.wait(batchTasks);

      for (var result in batchResults) {
        if (result != null) {
          optimizedBytesList.add(result);
        }
      }
    }

    debugPrint(
      'Completed optimization: ${optimizedBytesList.length}/${bytesList.length} images optimized',
    );
    return optimizedBytesList;
  }

  /// Check if image needs optimization
  static bool needsOptimization(int fileSizeBytes, int width, int height) {
    return fileSizeBytes > maxFileSize ||
        width > maxWidth ||
        height > maxHeight;
  }
}
