import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class LoadingIndicator extends StatelessWidget {
  // strokeWidth and color,  can be customized if needed
  final double strokeWidth;
  final Color? color;

  const LoadingIndicator({super.key, this.strokeWidth = 4.0, this.color});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SpinKitDoubleBounce(
        color: color ?? Theme.of(context).colorScheme.tertiary,
        size: strokeWidth * 10,
      ),
    );
  }
}
