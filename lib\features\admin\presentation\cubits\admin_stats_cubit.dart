import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/admin/domain/repos/admin_stats_repo.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_stats_state.dart';

class AdminStatsCubit extends Cubit<AdminStatsState> {
  final AdminStatsRepo statsRepo;

  AdminStatsCubit({required this.statsRepo}) : super(AdminStatsInitial());

  // Load all statistics
  Future<void> loadStats() async {
    emit(AdminStatsLoading());
    try {
      final stats = await statsRepo.getSystemStats();
      final topPosts = await statsRepo.getTopPosts();

      emit(AdminStatsLoaded(stats: stats, topPosts: topPosts));
    } catch (e) {
      emit(AdminStatsError('Failed to load statistics: $e'));
    }
  }

  // Refresh statistics
  Future<void> refreshStats() async {
    await loadStats();
  }

  // Clear error state
  void clearError() {
    if (state is AdminStatsError) {
      emit(AdminStatsInitial());
    }
  }
}
