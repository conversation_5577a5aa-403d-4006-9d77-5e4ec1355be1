/*
Register Here

On this Page a new user can register with their:
  - name
  - email
  - password
  - confirm password

  Once the user is successfully registered, the user is redirected to the home page.
  If user already has an account, they can navigate to login page.
 */

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:moneymouthy/features/auth/presentation/components/my_button.dart';
import 'package:moneymouthy/features/auth/presentation/components/my_text_field.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_states.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';
import 'package:toastification/toastification.dart';

class RegisterPage extends StatefulWidget {
  final VoidCallback? togglePages;
  const RegisterPage({super.key, this.togglePages});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final nameController = TextEditingController();
  final confirmPwController = TextEditingController();

  // register button pressed
  void register() {
    // Prevent multiple register attempts if already loading
    final currentState = context.read<AuthCubit>().state;
    if (currentState is AuthLoading) {
      return;
    }

    // prepare info
    final String name = nameController.text.trim();
    final String pw = passwordController.text;
    final String email = emailController.text.trim();
    final String confirmPw = confirmPwController.text;

    // auth cubit
    final authCubit = context.read<AuthCubit>();
    if (email.isNotEmpty &&
        pw.isNotEmpty &&
        name.isNotEmpty &&
        confirmPw.isNotEmpty) {
      //ensure password match
      if (pw == confirmPw) {
        authCubit.register(email, pw, name);
      } else {
        // password not match
        toastification.show(
          alignment: Alignment.topCenter,
          title: const Text('Validation Failed'),
          description: const Text('Passwords do not match'),
          type: ToastificationType.warning,
          style: ToastificationStyle.flatColored,
          autoCloseDuration: const Duration(seconds: 3),
        );
      }
    }
    // Empty
    else {
      toastification.show(
        alignment: Alignment.topCenter,
        title: const Text('Validation Failed'),
        description: const Text('Please complete all the fields'),
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: const Duration(seconds: 3),
      );
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPwController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthError) {
          // Show error message only
          toastification.show(
            alignment: Alignment.topCenter,
            title: const Text('Registration Failed'),
            description: Text(state.message),
            type: ToastificationType.error,
            style: ToastificationStyle.flatColored,
            autoCloseDuration: const Duration(seconds: 4),
          );
        }
      },
      child: ConstrainedScaffold(
        resizeToAvoidBottomInset: true,
        body: SingleChildScrollView(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - kToolbarHeight,
            ),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 25.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // logo
                    Icon(
                      Icons.lock_open_rounded,
                      size: 80,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 25),
                    // create an account message
                    Text(
                      "Let's create an account for you!",
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 25),
                    // name textfield
                    MyTextField(
                      controller: nameController,
                      hintText: 'Name',
                      obscureText: false,
                    ),
                    const SizedBox(height: 10),
                    // email textfield
                    MyTextField(
                      controller: emailController,
                      hintText: 'Email',
                      obscureText: false,
                    ),
                    const SizedBox(height: 10),

                    // password textfield
                    MyTextField(
                      controller: passwordController,
                      hintText: 'Password',
                      obscureText: true,
                    ),
                    // confirm password textfield
                    const SizedBox(height: 10),
                    MyTextField(
                      controller: confirmPwController,
                      hintText: 'Confirm Password',
                      obscureText: true,
                    ),
                    const SizedBox(height: 20),

                    // register button with loading state
                    BlocBuilder<AuthCubit, AuthState>(
                      builder: (context, state) {
                        final isLoading = state is AuthLoading;
                        return MyButton(
                          onTap:
                              register, // Always allow tap - button will handle loading internally
                          text: isLoading ? 'Creating Account...' : 'Register',
                          isLoading: isLoading,
                        );
                      },
                    ),
                    // already a member? login now
                    const SizedBox(height: 50),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Already a member?',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const SizedBox(width: 4),
                        GestureDetector(
                          onTap: () {
                            if (widget.togglePages != null) {
                              // If we have toggle function (from AuthPage), use it
                              widget.togglePages!();
                            } else {
                              // Otherwise navigate to login route (from Landing page)
                              context.go('/login');
                            }
                          },
                          child: Text(
                            'Login Now',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.secondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
