import 'package:flutter/material.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:moneymouthy/features/admin/presentation/pages/admin_home_page.dart';
import 'package:moneymouthy/features/admin/presentation/pages/admin_settings_page.dart';
import 'package:moneymouthy/features/admin/presentation/pages/admin_tools_page.dart';
import 'package:moneymouthy/features/admin/presentation/pages/admin_users_page.dart';
import 'package:moneymouthy/features/admin/presentation/pages/admin_wallets_page.dart';
import 'package:moneymouthy/features/auth/domain/entities/app_user.dart';

import 'package:moneymouthy/features/post/domain/entities/post.dart';

import 'package:moneymouthy/responsive/constrained_scaffold.dart';

class AdminPage extends StatefulWidget {
  final AppUser currentAdmin;
  const AdminPage({super.key, required this.currentAdmin});

  @override
  State<AdminPage> createState() => AdminPageState();
}

class AdminPageState extends State<AdminPage> {
  int _selectedIndex = 0;
  PostCategory? userSelectedCategory;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void switchTab(int index) {
    if (index >= 0 && index < 6 && index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });

      if (index == 0) {
        loadData();
      }
    }
  }

  void loadData() {}

  Widget _buildHomeFeed() {
    return AdminHomePage();
  }

  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeFeed(); // Admin Home
      case 1:
        return const AppUsersPage();
      case 2:
        return const AdminWalletsPage();
      case 3:
        return const AdminToolsPage();

      case 4:
        return const AdminSettingsPage();
      default:
        return _buildHomeFeed();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      body: Column(
        children: [
          Expanded(child: _buildCurrentPage()),

          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(blurRadius: 20, color: Colors.black.withAlpha(25)),
              ],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 10.0,
                vertical: 8,
              ),
              child: GNav(
                tabBorderRadius: 20,
                rippleColor: Theme.of(
                  context,
                ).colorScheme.tertiary.withAlpha(25),
                hoverColor: Theme.of(
                  context,
                ).colorScheme.tertiary.withAlpha(13),
                gap: 4,
                activeColor: Theme.of(
                  context,
                ).colorScheme.tertiary.withAlpha(200),
                iconSize: 25,
                textSize: 16,
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 5,
                ),
                duration: const Duration(milliseconds: 400),
                tabBackgroundColor: Theme.of(
                  context,
                ).colorScheme.primary.withAlpha(25),
                color: Theme.of(context).colorScheme.primary,
                tabs: const [
                  GButton(icon: Icons.dashboard, text: 'Dashboard'), // idx 0
                  GButton(icon: Icons.group, text: 'Users'), // idx 1
                  GButton(icon: Icons.wallet, text: 'Wallets'), // idx 2
                  // GButton(icon: Icons.post_add_sharp, text: 'Posts'),
                  GButton(
                    icon: Icons.admin_panel_settings,
                    text: 'Tools',
                  ), // idx 3
                  GButton(icon: Icons.settings, text: 'Settings'), // idx 4
                ],
                selectedIndex: _selectedIndex,
                onTabChange: switchTab,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
