{"indexes": [{"collectionGroup": "posts_new", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts_new", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts_new", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts_new", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "DESCENDING"}, {"fieldPath": "postCost", "order": "DESCENDING"}]}], "fieldOverrides": []}