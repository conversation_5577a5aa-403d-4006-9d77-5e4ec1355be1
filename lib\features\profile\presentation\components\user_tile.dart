import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:moneymouthy/features/profile/presentation/pages/profile_page.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';

class UserTile extends StatefulWidget {
  final ProfileUser user;
  const UserTile({super.key, required this.user});

  @override
  State<UserTile> createState() => _UserTileState();
}

class _UserTileState extends State<UserTile> {
  bool isFollowing = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkFollowingStatus();
  }

  void _checkFollowingStatus() {
    final currentUser = context.read<AuthCubit>().currentUser;
    if (currentUser != null) {
      // Check if current user is following this user
      context.read<CurrentUserProfileCubit>().fetchCurrentUserProfile(
        currentUser.uid,
      );
    }
  }

  void _toggleFollow() async {
    final currentUser = context.read<AuthCubit>().currentUser;
    if (currentUser == null) return;

    setState(() {
      isLoading = true;
    });

    final wasFollowing = isFollowing;

    try {
      // Optimistic update
      setState(() {
        isFollowing = !isFollowing;
        isLoading = false;
      });

      // Update backend (CurrentUserProfileCubit handles optimistic updates)
      await context.read<CurrentUserProfileCubit>().toggleFollow(
        currentUser.uid,
        widget.user.uid,
      );
    } catch (e) {
      // Rollback on error
      if (mounted) {
        setState(() {
          isFollowing = wasFollowing;
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CurrentUserProfileCubit, ProfileStates>(
      builder: (context, state) {
        if (state is ProfileLoaded) {
          isFollowing = state.profileUser.following.contains(widget.user.uid);
        }

        return ListTile(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ProfilePage(uid: widget.user.uid),
              ),
            );
          },
          leading: CircleAvatar(
            radius: 25,
            backgroundColor: Theme.of(
              context,
            ).colorScheme.primary.withAlpha(50),
            child: widget.user.profileImageUrl.isNotEmpty
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: widget.user.profileImageUrl,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Icon(
                        Icons.person,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      errorWidget: (context, url, error) => Icon(
                        Icons.person,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  )
                : Icon(
                    Icons.person,
                    color: Theme.of(context).colorScheme.primary,
                  ),
          ),
          title: Text(
            widget.user.name,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          subtitle: Text(
            widget.user.bio.isNotEmpty ? widget.user.bio : widget.user.email,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary.withAlpha(150),
            ),
          ),
          trailing: SizedBox(
            width: 80,
            child: ElevatedButton(
              onPressed: isLoading ? null : _toggleFollow,
              style: ElevatedButton.styleFrom(
                backgroundColor: isFollowing
                    ? Theme.of(context).colorScheme.surface
                    : Theme.of(context).colorScheme.primary,
                foregroundColor: isFollowing
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: const Size(70, 32),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 1,
                  ),
                ),
              ),
              child: isLoading
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: LoadingIndicator(
                        strokeWidth: 2,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    )
                  : Text(
                      isFollowing ? 'Unfollow' : 'Follow',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }
}
