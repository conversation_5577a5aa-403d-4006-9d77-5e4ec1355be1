import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/features/admin/domain/entities/admin_user.dart';
import 'package:moneymouthy/features/admin/domain/repos/admin_repo.dart';
import 'package:moneymouthy/features/admin/presentation/cubits/admin_state.dart';

class AdminCubit extends Cubit<AdminState> {
  final AdminRepo adminRepo;
  AdminCubit({required this.adminRepo}) : super(AdminInitial());
  AdminUser? currentUser;

  Future<bool> isAdmin() async {
    try {
      emit(AdminLoading());
      currentUser = await adminRepo.getCurrentUser();

      if (currentUser != null && currentUser!.isAdmin) {
        emit(AdminVerified(currentUser!));
        return true; // Admin is verified
      } else {
        emit(AdminNotVerified());
        return false; // Admin is not verified
      }
    } catch (e) {
      emit(AdminError("Failed to check admin: $e"));
      return false;
    }
  }

  Future<void> verifyAdmin() async {
    try {
      emit(AdminLoading());
      currentUser = await adminRepo.getCurrentUser();

      if (currentUser != null && currentUser!.isAdmin) {
        emit(AdminVerified(currentUser!));
      } else {
        emit(AdminNotVerified());
      }
    } catch (e) {
      emit(AdminError("Failed to verify admin: $e"));
    }
  }

  void logout() async {
    try {
      emit(AdminLoading());
      await adminRepo.logout();
      currentUser = null;
      emit(AdminNotVerified());
    } catch (e) {
      emit(AdminError("Failed to logout: $e"));
    }
  }
}
