/*

Admin Repository

- check if user is admin
- logout

*/

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:moneymouthy/features/admin/domain/entities/admin_user.dart';
import 'package:moneymouthy/features/admin/domain/repos/admin_repo.dart';

class FirebaseAdminRepo implements AdminRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
  final FirebaseAuth firebaseAuth = FirebaseAuth.instance;

  @override
  Future<bool> checkAdmin(String uid) async {
    try {
      // First check if user exists in regular users collection
      final userDoc = await firebaseFirestore
          .collection('users')
          .doc(uid)
          .get();

      if (!userDoc.exists) {
        return false; // User must exist as regular user first
      }

      // Then check if user exists in admins collection
      final adminDoc = await firebaseFirestore
          .collection('admins')
          .doc(uid)
          .get();

      return adminDoc.exists;
    } catch (e) {
      throw Exception('Failed to check admin: $e');
    }
  }

  // get current user
  @override
  Future<AdminUser> getCurrentUser() async {
    final firebaseUser = firebaseAuth.currentUser;
    if (firebaseUser == null) throw Exception('User not logged in');
    return getAdminUser(firebaseUser.uid);
  }

  // get admin user
  @override
  Future<AdminUser> getAdminUser(String uid) async {
    try {
      // First get user data from users collection
      final userDoc = await firebaseFirestore
          .collection('users')
          .doc(uid)
          .get();

      if (!userDoc.exists) {
        throw Exception('User does not exist in users collection');
      }

      // Then verify admin status
      final adminDoc = await firebaseFirestore
          .collection('admins')
          .doc(uid)
          .get();

      if (!adminDoc.exists) {
        throw Exception('User is not an admin');
      }

      // Combine data from both collections
      final userData = userDoc.data()!;
      final adminData = adminDoc.data() ?? {};

      return AdminUser.fromJson({
        'uid': uid,
        'name': userData['name'] ?? adminData['name'] ?? '',
        'email': userData['email'] ?? adminData['email'] ?? '',
        'isAdmin': true,
      });
    } catch (e) {
      throw Exception('Failed to get admin: $e');
    }
  }

  @override
  Future<void> logout() async {
    await firebaseAuth.signOut();
  }
}
