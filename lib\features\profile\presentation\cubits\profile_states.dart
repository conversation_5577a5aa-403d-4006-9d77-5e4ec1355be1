import 'package:flutter/material.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:toastification/toastification.dart';

/// Profile States
///

abstract class ProfileStates {}

// initial
class ProfileInitial extends ProfileStates {}

// loading...
class ProfileLoading extends ProfileStates {}

// loaded
class ProfileLoaded extends ProfileStates {
  final ProfileUser profileUser;
  ProfileLoaded(this.profileUser);
}

// error

class ProfileError extends ProfileStates {
  final String message;

  ProfileError(this.message) {
    toastification.show(
      alignment: Alignment.topCenter,
      title: Text('Error'),
      description: Text(message),
      type: ToastificationType.error,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: Duration(seconds: 3),
    );
  }
}
