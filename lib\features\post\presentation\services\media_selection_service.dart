import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toastification/toastification.dart';
import 'package:media_kit/media_kit.dart';

class MediaFile {
  final String? path;
  final Uint8List? bytes;
  final String name;
  final MediaType type;
  final Duration? duration; // Added duration for videos

  MediaFile({
    this.path,
    this.bytes,
    required this.name,
    required this.type,
    this.duration,
  });

  bool get isWeb => kIsWeb;
}

enum MediaType { image, video }

class MediaSelectionService {
  static final ImagePicker _imagePicker = ImagePicker();

  // Capture single image from camera
  static Future<MediaFile?> captureImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: image.name,
            type: MediaType.image,
          );
        } else {
          return MediaFile(
            path: image.path,
            name: image.name,
            type: MediaType.image,
          );
        }
      }
    } catch (e) {
      debugPrint('Error capturing image from camera: $e');
      _showErrorToast('Failed to capture image from camera');
    }
    return null;
  }

  // Select single image from gallery
  static Future<MediaFile?> selectImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: image.name,
            type: MediaType.image,
          );
        } else {
          return MediaFile(
            path: image.path,
            name: image.name,
            type: MediaType.image,
          );
        }
      }
    } catch (e) {
      debugPrint('Error selecting image from gallery: $e');
      _showErrorToast('Failed to select image from gallery');
    }
    return null;
  }

  // Select multiple images from gallery
  static Future<List<MediaFile>> selectMultipleImagesFromGallery() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      List<MediaFile> mediaFiles = [];
      for (XFile image in images) {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          mediaFiles.add(
            MediaFile(bytes: bytes, name: image.name, type: MediaType.image),
          );
        } else {
          mediaFiles.add(
            MediaFile(
              path: image.path,
              name: image.name,
              type: MediaType.image,
            ),
          );
        }
      }
      return mediaFiles;
    } catch (e) {
      debugPrint('Error selecting multiple images: $e');
      _showErrorToast('Failed to select images');
      return [];
    }
  }

  // Record video from camera
  static Future<MediaFile?> recordVideoFromCamera({
    int maxDurationSeconds = 30,
  }) async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: Duration(seconds: maxDurationSeconds),
      );

      if (video != null) {
        // Validate video duration using proper media analysis
        final validationResult = await _validateVideoDuration(
          video,
          maxDurationSeconds,
        );
        if (!validationResult.isValid) {
          _showDurationExceededToast(
            maxDurationSeconds,
            validationResult.actualDuration,
          );
          // Clean up the file if it exists on mobile
          if (!kIsWeb && video.path.isNotEmpty) {
            try {
              final file = File(video.path);
              if (await file.exists()) {
                await file.delete();
              }
            } catch (e) {
              debugPrint('Error cleaning up invalid video file: $e');
            }
          }
          return null;
        }

        if (kIsWeb) {
          final bytes = await video.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: video.name,
            type: MediaType.video,
            duration: validationResult.actualDuration,
          );
        } else {
          return MediaFile(
            path: video.path,
            name: video.name,
            type: MediaType.video,
            duration: validationResult.actualDuration,
          );
        }
      }
    } catch (e) {
      debugPrint('Error recording video from camera: $e');
      _showErrorToast('Failed to record video');
    }
    return null;
  }

  // Select video from gallery
  static Future<MediaFile?> selectVideoFromGallery({
    int maxDurationSeconds = 30,
  }) async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: Duration(seconds: maxDurationSeconds),
      );

      if (video != null) {
        // Validate video duration using proper media analysis
        final validationResult = await _validateVideoDuration(
          video,
          maxDurationSeconds,
        );
        if (!validationResult.isValid) {
          _showDurationExceededToast(
            maxDurationSeconds,
            validationResult.actualDuration,
          );
          return null;
        }

        if (kIsWeb) {
          final bytes = await video.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: video.name,
            type: MediaType.video,
            duration: validationResult.actualDuration,
          );
        } else {
          return MediaFile(
            path: video.path,
            name: video.name,
            type: MediaType.video,
            duration: validationResult.actualDuration,
          );
        }
      }
    } catch (e) {
      debugPrint('Error selecting video from gallery: $e');
      _showErrorToast('Failed to select video');
    }
    return null;
  }

  // Improved video duration validation using media_kit
  static Future<VideoValidationResult> _validateVideoDuration(
    XFile video,
    int maxDurationSeconds,
  ) async {
    try {
      Duration? actualDuration;

      try {
        final player = Player();
        await player.open(Media(video.path));

        // Wait for media to be loaded
        await Future.delayed(const Duration(milliseconds: 500));

        actualDuration = player.state.duration;
        await player.dispose();
      } catch (e) {
        debugPrint('Error using media_kit for duration: $e');

        // Fallback to file size estimation
        final file = File(video.path);
        final sizeInMB = (await file.length()) / (1024 * 1024);
        final estimatedDurationSeconds = (sizeInMB / 0.5) * 60;
        actualDuration = Duration(seconds: estimatedDurationSeconds.round());
      }
      // }

      if (actualDuration == null) {
        // If we can't determine duration, reject for security
        return VideoValidationResult(
          isValid: false,
          actualDuration: Duration.zero,
          reason: 'Unable to determine video duration',
        );
      }

      final isValid = actualDuration.inSeconds <= maxDurationSeconds;
      return VideoValidationResult(
        isValid: isValid,
        actualDuration: actualDuration,
        reason: isValid ? null : 'Video duration exceeds limit',
      );
    } catch (e) {
      debugPrint('Error validating video duration: $e');
      // Fail closed - reject if we can't validate
      return VideoValidationResult(
        isValid: false,
        actualDuration: Duration.zero,
        reason: 'Validation error occurred',
      );
    }
  }

  // Get accurate video metadata using media_kit
  static Future<VideoMetadata?> getVideoMetadata(String path) async {
    try {
      if (kIsWeb) {
        return null; // Limited metadata access on web
      }

      final player = Player();
      await player.open(Media(path));

      // Wait for media to be loaded
      await Future.delayed(const Duration(milliseconds: 500));

      final duration = player.state.duration;
      final file = File(path);
      final size = await file.length();

      await player.dispose();

      return VideoMetadata(duration: duration, size: size, path: path);
    } catch (e) {
      debugPrint('Error getting video metadata: $e');
      return null;
    }
  }

  // Toast notification helpers
  static void _showDurationExceededToast(
    int maxDuration,
    Duration? actualDuration,
  ) {
    final actualSeconds = actualDuration?.inSeconds ?? 0;
    toastification.show(
      alignment: Alignment.topCenter,
      title: const Text('Video Duration Exceeded'),
      description: Text(
        actualDuration != null
            ? 'Video is ${actualSeconds}s long. Maximum allowed is ${maxDuration}s.'
            : 'Video duration exceeds $maxDuration seconds limit',
      ),
      type: ToastificationType.error,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: const Duration(seconds: 4),
    );
  }

  static void _showErrorToast(String message) {
    toastification.show(
      alignment: Alignment.topCenter,
      title: const Text('Error'),
      description: Text(message),
      type: ToastificationType.error,
      style: ToastificationStyle.flatColored,
      autoCloseDuration: const Duration(seconds: 3),
    );
  }
}

// Data classes for better type safety
class VideoValidationResult {
  final bool isValid;
  final Duration actualDuration;
  final String? reason;

  VideoValidationResult({
    required this.isValid,
    required this.actualDuration,
    this.reason,
  });
}

class VideoMetadata {
  final Duration? duration;
  final int size;
  final String path;

  VideoMetadata({this.duration, required this.size, required this.path});
}
