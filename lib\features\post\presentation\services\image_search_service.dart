// lib/features/post/services/image_search_service.dart
import 'package:flutter/foundation.dart';
import 'package:moneymouthy/features/post/presentation/services/media_selection_service.dart';
import 'package:moneymouthy/features/post/presentation/services/serper_service.dart';

class ImageSearchService {
  static final SerperService _serperService = SerperService();

  /// Search for images using the query
  static Future<List<String>> searchImages(String query) async {
    if (query.trim().isEmpty) {
      throw Exception('Search query cannot be empty');
    }

    try {
      return await _serperService.searchImages(query.trim());
    } catch (e) {
      debugPrint('Error searching images: $e');
      throw Exception('Failed to search images: $e');
    }
  }

  /// Download image and convert to MediaFile
  static Future<MediaFile?> downloadImageAsMediaFile(String url) async {
    try {
      final imageBytes = await _serperService.downloadImage(url);
      if (imageBytes != null && imageBytes.isNotEmpty) {
        // Validate that the bytes represent a valid image
        if (_isValidImageBytes(imageBytes)) {
          return MediaFile(
            bytes: imageBytes,
            path: null,
            name: _generateImageName(url),
            type: MediaType.image,
          );
        } else {
          debugPrint('Downloaded bytes are not a valid image: $url');
          return null;
        }
      }
      debugPrint('Failed to download image or received empty bytes: $url');
      return null;
    } catch (e) {
      debugPrint('Error downloading image: $e');
      return null;
    }
  }

  /// Validate if bytes represent a valid image
  static bool _isValidImageBytes(Uint8List bytes) {
    if (bytes.isEmpty) return false;

    // More lenient validation - just check if we have reasonable image data
    if (bytes.length < 100) return false; // Too small to be a valid image

    // Check for common image file signatures
    // JPEG: FF D8
    if (bytes.length >= 2 && bytes[0] == 0xFF && bytes[1] == 0xD8) {
      return true;
    }

    // PNG: 89 50 4E 47
    if (bytes.length >= 4 &&
        bytes[0] == 0x89 &&
        bytes[1] == 0x50 &&
        bytes[2] == 0x4E &&
        bytes[3] == 0x47) {
      return true;
    }

    // GIF: 47 49 46 38 (GIF8) or 47 49 46 38 (GIF9)
    if (bytes.length >= 4 &&
        bytes[0] == 0x47 &&
        bytes[1] == 0x49 &&
        bytes[2] == 0x46 &&
        (bytes[3] == 0x38 || bytes[3] == 0x39)) {
      return true;
    }

    // WebP: 52 49 46 46 (RIFF)
    if (bytes.length >= 4 &&
        bytes[0] == 0x52 &&
        bytes[1] == 0x49 &&
        bytes[2] == 0x46 &&
        bytes[3] == 0x46) {
      return true;
    }

    // BMP: 42 4D
    if (bytes.length >= 2 && bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return true;
    }

    // If none of the signatures match but we have substantial data, assume it's valid
    // This handles cases where proxy responses might have different headers
    return bytes.length > 1000;
  }

  /// Generate a proper image name from URL
  static String _generateImageName(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        final lastSegment = pathSegments.last;
        if (lastSegment.contains('.')) {
          return lastSegment;
        }
      }
      // Fallback to timestamp-based name
      return 'searched_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
    } catch (e) {
      return 'searched_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
    }
  }

  /// Download multiple images and convert to MediaFiles
  static Future<List<MediaFile>> downloadImagesAsMediaFiles(
    List<String> urls,
  ) async {
    if (urls.isEmpty) return [];

    debugPrint('Starting download of ${urls.length} images...');
    final List<MediaFile> mediaFiles = [];
    int successCount = 0;
    int failureCount = 0;

    // Download images with limited concurrency to avoid overwhelming the server
    const int batchSize = 3;
    for (int i = 0; i < urls.length; i += batchSize) {
      final end = (i + batchSize < urls.length) ? i + batchSize : urls.length;
      final batch = urls.sublist(i, end);

      // Download batch in parallel
      final batchTasks = batch.map((url) => downloadImageAsMediaFile(url));
      final batchResults = await Future.wait(batchTasks);

      // Process results
      for (final mediaFile in batchResults) {
        if (mediaFile != null) {
          mediaFiles.add(mediaFile);
          successCount++;
        } else {
          failureCount++;
        }
      }
    }

    debugPrint(
      'Image download completed: $successCount successful, $failureCount failed',
    );

    if (mediaFiles.isEmpty && urls.isNotEmpty) {
      throw Exception(
        'All image downloads failed. Please check your internet connection and try again.',
      );
    }

    return mediaFiles;
  }
}
