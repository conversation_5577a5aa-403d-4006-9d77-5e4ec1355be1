// lib/features/post/services/image_search_service.dart
import 'package:flutter/foundation.dart';
import 'package:moneymouthy/features/post/presentation/services/media_selection_service.dart';
import 'package:moneymouthy/features/post/presentation/services/serper_service.dart';

class ImageSearchService {
  static final SerperService _serperService = SerperService();

  /// Search for images using the query
  static Future<List<String>> searchImages(String query) async {
    if (query.trim().isEmpty) {
      throw Exception('Search query cannot be empty');
    }

    try {
      return await _serperService.searchImages(query.trim());
    } catch (e) {
      debugPrint('Error searching images: $e');
      throw Exception('Failed to search images: $e');
    }
  }

  /// Download image and convert to MediaFile
  static Future<MediaFile?> downloadImageAsMediaFile(String url) async {
    try {
      final imageBytes = await _serperService.downloadImage(url);
      if (imageBytes != null) {
        return MediaFile(
          bytes: imageBytes,
          path: null,
          name: url,
          type: MediaType.image,
        );
      }
      return null;
    } catch (e) {
      debugPrint('Error downloading image: $e');
      return null;
    }
  }

  /// Download multiple images and convert to MediaFiles
  static Future<List<MediaFile>> downloadImagesAsMediaFiles(
    List<String> urls,
  ) async {
    final List<MediaFile> mediaFiles = [];

    for (String url in urls) {
      final mediaFile = await downloadImageAsMediaFile(url);
      if (mediaFile != null) {
        mediaFiles.add(mediaFile);
      }
    }

    return mediaFiles;
  }
}
