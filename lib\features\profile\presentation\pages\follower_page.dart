/*
* Follower Page
* This page is used to display the followers/following of a user

This willl diaplay these things:
*  - List of followers/following

* */

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:moneymouthy/features/profile/presentation/components/user_tile.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';

class FollowerPage extends StatelessWidget {
  final List<String> followers;
  final List<String> following;
  final String? currentUserId;

  const FollowerPage({
    super.key,
    required this.followers,
    required this.following,
    this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: currentUserId != null ? 3 : 2,
      child: ConstrainedScaffold(
        // App Bar
        appBar: AppBar(
          // Tab Bar
          bottom: TabBar(
            dividerColor: Colors.transparent,
            labelColor: Theme.of(context).colorScheme.inversePrimary,
            unselectedLabelColor: Theme.of(context).colorScheme.primary,
            tabs: [
              const Tab(text: 'Followers'),
              const Tab(text: 'Following'),
              if (currentUserId != null) const Tab(text: 'Unfollowing'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // followers
            _buildUserList(followers, 'No followers yet...', context),
            // following
            _buildUserList(following, 'Not following anyone yet...', context),
            // unfollowing (only show if currentUserId is provided)
            if (currentUserId != null) _buildUnfollowingUserList(context),
          ],
        ),
      ),
    );
  }

  // build user list , given a list of profile uids
  Widget _buildUserList(
    List<String> uids,
    String emptyMessage,
    BuildContext context,
  ) {
    return uids.isEmpty
        ? Center(child: Text(emptyMessage))
        : ListView.builder(
            itemCount: uids.length,
            itemBuilder: (context, index) {
              // get each user by uid
              final uid = uids[index];
              return FutureBuilder(
                future: context.read<ProfileCubit>().getUserProfile(uid),
                builder: (context, snapshot) {
                  // user loaded
                  if (snapshot.hasData) {
                    final user = snapshot.data!;
                    return UserTile(user: user);
                  }
                  // loading...
                  else if (snapshot.connectionState ==
                      ConnectionState.waiting) {
                    return ListTile(title: Text('Loading...'));
                  }
                  // not found...
                  else {
                    return ListTile(title: Text('User Not Found...'));
                  }
                },
              );
            },
          );
  }

  // Build unfollowing users list
  Widget _buildUnfollowingUserList(BuildContext context) {
    if (currentUserId == null) {
      return const Center(child: Text('Not available'));
    }

    return FutureBuilder<List<ProfileUser>>(
      future: context.read<ProfileCubit>().getUnfollowingUsers(currentUserId!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final unfollowingUsers = snapshot.data ?? [];

        if (unfollowingUsers.isEmpty) {
          return const Center(child: Text('You are following everyone!'));
        }

        return ListView.builder(
          itemCount: unfollowingUsers.length,
          itemBuilder: (context, index) {
            final user = unfollowingUsers[index];
            return UserTile(user: user);
          },
        );
      },
    );
  }
}
