import 'package:moneymouthy/features/poll/domain/entities/poll.dart';

abstract class PollRepo {
  // Create a new poll
  Future<void> createPoll(Poll poll);

  // Get poll by ID
  Future<Poll?> getPollById(String pollId);

  // Get poll by post ID
  Future<Poll?> getPollByPostId(String postId);

  // Vote on a poll
  Future<void> voteOnPoll(String pollId, String userId, PollOption option);

  // Remove vote from a poll
  Future<void> removeVoteFromPoll(String pollId, String userId);

  // Update poll (for admin purposes)
  Future<void> updatePoll(Poll poll);

  // Delete poll
  Future<void> deletePoll(String pollId);

  // Get all polls created by a user
  Future<List<Poll>> getPollsByCreator(String creatorId);

  // Get polls that a user has voted on
  Future<List<Poll>> getPollsVotedByUser(String userId);

  // Stream poll updates (for real-time updates)
  Stream<Poll?> streamPoll(String pollId);
}
