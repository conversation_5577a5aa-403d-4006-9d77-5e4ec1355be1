import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/custom_icon_button.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/current_user_profile_cubit.dart';
import 'package:moneymouthy/features/profile/presentation/cubits/profile_states.dart';
import 'package:moneymouthy/features/post/presentation/components/price_slider.dart';
import 'package:moneymouthy/features/wallet/presentation/cubits/wallet_balance_cubit.dart';
import 'package:moneymouthy/globals.dart';

import '../../../../themes/theme_cubit.dart';

class Sidebar extends StatelessWidget {
  final PostCategory? selectedCategory;
  final Function(PostCategory?) onCategoryChanged;

  const Sidebar({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
  });

  @override
  Widget build(BuildContext context) {
    final themeCubit = context.watch<ThemeCubit>();

    // is dark mode
    bool isDarkMode = themeCubit.isDarkMode;
    return Container(
      width: kIsWeb ? 115 : 100,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withAlpha(50),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Profile Section
          _buildProfileSection(context),

          // Account Balance Section
          _buildBalanceSection(context),
          // Price Slider Section
          const PriceSlider(isEditable: true),
          // Categories Section
          _buildCategoriesSection(context),

          // Stats Section
          _buildStatsSection(context),

          // const Spacer(),
          // Theme Button
          Column(
            children: [
              Text('Dark Mode'),
              Transform.scale(
                scale: 0.7,
                child: CupertinoSwitch(
                  value: isDarkMode,
                  onChanged: (value) {
                    themeCubit.toggleTheme();
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),
          // Logout Button
          _buildLogoutButton(context),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context) {
    return BlocBuilder<CurrentUserProfileCubit, ProfileStates>(
      builder: (context, state) {
        if (state is ProfileLoaded) {
          final user = state.profileUser;
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceTint.withAlpha(100),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(6),
                bottomRight: Radius.circular(6),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Profile Picture
                Container(
                  width: 40,
                  height: 40,

                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: user.profileImageUrl.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: user.profileImageUrl,
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              Center(child: const LoadingIndicator()),
                          errorWidget: (context, url, error) => Icon(
                            Icons.person,
                            size: 40,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        )
                      : Icon(
                          Icons.person,
                          size: 40,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                ),
                const SizedBox(width: 6),

                Text(
                  user.name,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Container(
          height: 70,
          padding: const EdgeInsets.all(6),
          child: Center(child: const LoadingIndicator()),
        );
      },
    );
  }

  Widget _buildBalanceSection(BuildContext context) {
    return BlocBuilder<WalletBalanceCubit, double>(
      builder: (context, balance) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          padding: const EdgeInsets.all(4),
          width: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceTint.withAlpha(30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Text(
                'Balance',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                '\$${balance.toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 14,
                ),
              ),
              // go to wallet page
              CustomIconButton(
                label: 'ReUp',
                icon: Icons.add,
                backgroundColor: Theme.of(context).colorScheme.tertiary,
                onTap: () => homePageKey.currentState?.switchTab(1),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoriesSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        // boder
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Categories',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),

          // // All Posts Button
          // _buildCategoryButton(
          //   context,
          //   'All Posts',
          //   null,
          //   selectedCategory == null,
          // ),
          const SizedBox(height: 8),

          // Category Buttons
          ...PostCategory.values.map(
            (category) => _buildCategoryButton(
              context,
              category.name.replaceFirst(
                category.name[0],
                category.name[0].toUpperCase(),
              ),
              category,
              selectedCategory == category,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryButton(
    BuildContext context,
    String title,
    PostCategory? category,
    bool isSelected,
  ) {
    final categoryColor = _getCategoryColor(category);

    return SizedBox(
      width: double.infinity,
      child: GestureDetector(
        onTap: () => onCategoryChanged(category),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          padding: const EdgeInsets.symmetric(vertical: 5),
          decoration: BoxDecoration(
            color: isSelected ? categoryColor : categoryColor.withAlpha(30),
            border: Border.all(
              color: isSelected
                  ? Colors.transparent
                  : categoryColor.withAlpha(100),
              width: 0.8,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          alignment: Alignment.center,
          child: Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: isSelected
                  ? Colors.white
                  : Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(PostCategory? category) {
    if (category == null) return Colors.grey;

    switch (category) {
      case PostCategory.politics:
        return Colors.blue;
      case PostCategory.sports:
        return Colors.green;
      case PostCategory.entertainment:
        return Colors.purple;
      case PostCategory.news:
        return Colors.orange;
      case PostCategory.sex:
        return Colors.pink;
      case PostCategory.religion:
        return const Color.fromARGB(255, 0, 0, 0);
    }
  }

  Widget _buildStatsSection(BuildContext context) {
    return BlocBuilder<CurrentUserProfileCubit, ProfileStates>(
      builder: (context, state) {
        if (state is ProfileLoaded) {
          final user = state.profileUser;
          return Container(
            // margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
            padding: const EdgeInsets.all(8),
            margin: const EdgeInsets.symmetric(vertical: 10),
            decoration: BoxDecoration(
              // borderRadius: BorderRadius.circular(12),
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                  width: 1,
                ),
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildStatItem(
                  context,
                  'Followers',
                  user.followers.length.toString(),
                ),
                const SizedBox(height: 6),
                _buildStatItem(
                  context,
                  'Following',
                  user.following.length.toString(),
                ),
                const SizedBox(height: 6),
                _buildUnfollowingStatItem(context, user.uid),
                // const SizedBox(height: 12),
                // _buildStatItem(context, 'Posts', '0'),
              ],
            ),
          );
        }

        return const SizedBox(height: 100);
      },
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildUnfollowingStatItem(BuildContext context, String currentUid) {
    return FutureBuilder<List<ProfileUser>>(
      future: context.read<ProfileCubit>().getUnfollowingUsers(currentUid),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          final unfollowingCount = snapshot.data!.length;
          return _buildStatItem(
            context,
            'Unfollowing',
            unfollowingCount.toString(),
          );
        } else {
          return _buildStatItem(context, 'Unfollowing', '...');
        }
      },
    );
  }

  // let use CustomIconButton
  Widget _buildLogoutButton(BuildContext context) {
    return CustomIconButton(
      label: 'Logout',
      icon: Icons.logout,
      backgroundColor: Colors.red,
      onTap: () => _showLogoutConfirmation(context),
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                context.read<AuthCubit>().logout();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
