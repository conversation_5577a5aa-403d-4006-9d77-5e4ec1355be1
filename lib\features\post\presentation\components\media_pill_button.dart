import 'package:flutter/material.dart';
import 'package:moneymouthy/features/post/presentation/services/media_selection_service.dart';

class MediaPillButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color backgroundColor;
  final VoidCallback onTap;
  final bool isSelected;

  const MediaPillButton({
    super.key,
    required this.icon,
    required this.label,
    required this.backgroundColor,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: !isSelected
              ? backgroundColor
              : backgroundColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: backgroundColor, width: isSelected ? 0 : 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 12,
              color: !isSelected ? Colors.white : backgroundColor,
            ),
            const SizedBox(width: 3),
            Text(
              label,
              style: TextStyle(
                color: !isSelected ? Colors.white : backgroundColor,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MediaPillButtonRow extends StatelessWidget {
  final Function(List<MediaFile>) onImagesSelected;
  final Function(MediaFile) onVideoSelected;
  final Function(MediaFile) onVideoAdSelected;
  final Function() onPollSelected;
  final Function() onLinksSelected;
  final Function() onImageSearchSelected;
  final Function() onEmojiSelected;
  final bool hasImages;
  final bool hasVideo;
  final bool hasVideoAd;
  final bool hasPoll;
  final bool hasLinks;
  final bool hasImageSearch;

  const MediaPillButtonRow({
    super.key,
    required this.onImagesSelected,
    required this.onVideoSelected,
    required this.onVideoAdSelected,
    required this.onPollSelected,
    this.hasImages = false,
    this.hasVideo = false,
    this.hasVideoAd = false,
    this.hasPoll = false,
    this.hasLinks = false,
    this.hasImageSearch = false,
    required this.onLinksSelected,
    required this.onImageSearchSelected,
    required this.onEmojiSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      runSpacing: 4,
      children: [
        MediaPillButton(
          icon: Icons.camera_alt,
          label: 'Camera',
          backgroundColor: Colors.blue.shade700,
          isSelected: hasImages,
          onTap: () => _handleCameraSelection(context),
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.photo_library,
          label: 'Gallery',
          backgroundColor: Colors.green.shade700,
          isSelected: hasImages,
          onTap: () => _handleGallerySelection(context),
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.videocam,
          label: 'Record',
          backgroundColor: Colors.red.shade700,
          isSelected: hasVideo,
          onTap: () => _handleVideoRecord(context),
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.video_library,
          label: 'Video(30)',
          backgroundColor: Colors.orange.shade700,
          isSelected: hasVideo,
          onTap: () => _handleVideoGallery(context),
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.video_camera_back,
          label: 'VideoAd(60)',
          backgroundColor: Colors.deepOrange.shade700,
          isSelected: hasVideoAd,
          onTap: () => _showVideoAdOptions(context),
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.poll,
          label: 'Poll',
          backgroundColor: Colors.purple.shade700,

          isSelected: hasPoll,
          onTap: onPollSelected,
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.link,
          label: 'Links',
          backgroundColor: Colors.indigo.shade700,
          isSelected: hasLinks,
          onTap: onLinksSelected,
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.image_search,
          label: 'Search Images',
          backgroundColor: Colors.blueGrey.shade700,
          isSelected: hasImageSearch,
          onTap: onImageSearchSelected,
        ),
        const SizedBox(width: 8),
        MediaPillButton(
          icon: Icons.emoji_emotions,
          label: 'Emojis',
          backgroundColor: Colors.amber.shade700,
          isSelected: false,
          onTap: onEmojiSelected,
        ),
      ],
    );
  }

  Future<void> _handleCameraSelection(BuildContext context) async {
    final image = await MediaSelectionService.captureImageFromCamera();
    if (image != null) {
      onImagesSelected([image]);
    }
  }

  Future<void> _handleGallerySelection(BuildContext context) async {
    final images =
        await MediaSelectionService.selectMultipleImagesFromGallery();
    if (images.isNotEmpty) {
      onImagesSelected(images);
    }
  }

  Future<void> _handleVideoRecord(BuildContext context) async {
    final video = await MediaSelectionService.recordVideoFromCamera();
    if (video != null) {
      onVideoSelected(video);
    }
  }

  Future<void> _handleVideoGallery(BuildContext context) async {
    final video = await MediaSelectionService.selectVideoFromGallery();
    if (video != null) {
      onVideoSelected(video);
    }
  }

  void _showVideoAdOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'VideoAd (60 seconds)',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _handleVideoAdRecord(context);
                    },
                    icon: const Icon(Icons.videocam),
                    label: const Text('Record'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _handleVideoAdGallery(context);
                    },
                    icon: const Icon(Icons.video_library),
                    label: const Text('Gallery'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleVideoAdRecord(BuildContext context) async {
    final video = await MediaSelectionService.recordVideoFromCamera(
      maxDurationSeconds: 60,
    );
    if (video != null) {
      onVideoAdSelected(video);
    }
  }

  Future<void> _handleVideoAdGallery(BuildContext context) async {
    final video = await MediaSelectionService.selectVideoFromGallery(
      maxDurationSeconds: 60,
    );
    if (video != null) {
      onVideoAdSelected(video);
    }
  }
}
