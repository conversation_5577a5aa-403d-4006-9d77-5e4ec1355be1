/*
Admin User Management Repository
Handles CRUD operations for user management from admin panel
*/

import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';

abstract class AdminUserManagementRepo {
  // Get all users
  Future<List<ProfileUser>> getAllUsers();

  // Search users by name or email
  Future<List<ProfileUser>> searchUsers(String query);

  // Get user by ID
  Future<ProfileUser?> getUserById(String userId);

  // Block/Unblock user
  Future<void> toggleUserBlockStatus(String userId, bool isBlocked);

  // Delete user
  Future<void> deleteUser(String userId);

  // Create new user
  Future<void> createUser({
    required String email,
    required String name,
    String? bio,
    bool isAdmin = false,
  });

  // Update user profile
  Future<void> updateUserProfile(String userId, Map<String, dynamic> updates);

  // Get user statistics
  Future<Map<String, dynamic>> getUserStats(String userId);

  // Get users count
  Future<int> getUsersCount();

  // Get blocked users
  Future<List<ProfileUser>> getBlockedUsers();
}
