/*
Admin Statistics Repository
Handles fetching statistics and overview data for admin dashboard
*/

abstract class AdminStatsRepo {
  // Get total users count
  Future<int> getTotalUsersCount();

  // Get total posts count
  Future<int> getTotalPostsCount();

  // Get total wallet balance across all users
  Future<double> getTotalWalletBalance();

  // Get posts count by category
  Future<Map<String, int>> getPostsCountByCategory();

  // Get recent user registrations (last 7 days)
  Future<int> getRecentUserRegistrations();

  // Get recent posts count (last 7 days)
  Future<int> getRecentPostsCount();

  // Get top posts by engagement (likes + hates)
  Future<List<Map<String, dynamic>>> getTopPosts({int limit = 5});

  // Get system statistics
  Future<Map<String, dynamic>> getSystemStats();
}
