import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';
import 'package:url_launcher/url_launcher.dart';

class LinkManagementWidget extends StatefulWidget {
  final List<String> initialLinks;
  final Function(List<String>) onLinksChanged;
  final int maxLinks;

  const LinkManagementWidget({
    super.key,
    required this.initialLinks,
    required this.onLinksChanged,
    this.maxLinks = 5,
  });

  @override
  State<LinkManagementWidget> createState() => _LinkManagementWidgetState();
}

class _LinkManagementWidgetState extends State<LinkManagementWidget> {
  late List<String> _links;
  final TextEditingController _linkController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _links = List.from(widget.initialLinks);
  }

  @override
  void dispose() {
    _linkController.dispose();
    super.dispose();
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  String _formatUrl(String url) {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return 'https://$url';
    }
    return url;
  }

  void _addLink() {
    final link = _linkController.text.trim();
    if (link.isNotEmpty && _links.length < widget.maxLinks) {
      final formattedLink = _formatUrl(link);
      if (_isValidUrl(formattedLink) && !_links.contains(formattedLink)) {
        setState(() {
          _links.add(formattedLink);
          _linkController.clear();
        });
        widget.onLinksChanged(_links);
      } else {
        toastification.show(
          alignment: Alignment.topCenter,
          title: Text('Invalid URL'),
          description: Text('Please enter a valid URL or URL already exists'),
          type: ToastificationType.error,
          style: ToastificationStyle.flatColored,
          autoCloseDuration: Duration(seconds: 3),
        );
      }
    } else if (_links.length >= widget.maxLinks) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Links limit Exceeded'),
        description: Text('Maximum ${widget.maxLinks} links allowed'),
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
    }
  }

  void _removeLink(int index) {
    setState(() {
      _links.removeAt(index);
    });
    widget.onLinksChanged(_links);
  }

  Future<void> _previewLink(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      toastification.show(
        alignment: Alignment.topCenter,
        title: Text('Could not open link'),
        // description: Text('Please enter both email and password'),
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        autoCloseDuration: Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Add link input
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _linkController,
                decoration: InputDecoration(
                  hintText: 'Enter URL (e.g., moneymouthy.com)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                onSubmitted: (_) => _addLink(),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _addLink,
              icon: const Icon(Icons.add),
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
          ],
        ),

        if (_links.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Links (${_links.length}/${widget.maxLinks})',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),

          // Links list
          ...List.generate(_links.length, (index) {
            final link = _links[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.link,
                    size: 20,
                    color: Theme.of(context).colorScheme.tertiary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _previewLink(link),
                      child: Text(
                        link,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          decoration: TextDecoration.underline,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => _previewLink(link),
                    icon: const Icon(Icons.open_in_new),
                    iconSize: 18,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                  IconButton(
                    onPressed: () => _removeLink(index),
                    icon: const Icon(Icons.close),
                    iconSize: 18,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    color: Colors.red,
                  ),
                ],
              ),
            );
          }),
        ],
      ],
    );
  }
}

// Bottom sheet for link management
class LinkManagementBottomSheet extends StatelessWidget {
  final List<String> initialLinks;
  final Function(List<String>) onLinksChanged;

  const LinkManagementBottomSheet({
    super.key,
    required this.initialLinks,
    required this.onLinksChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 20, 20, MediaQuery.of(context).viewInsets.bottom + 20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          Text(
            'Manage Links',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          LinkManagementWidget(
            initialLinks: initialLinks,
            onLinksChanged: onLinksChanged,
          ),

          const SizedBox(height: 20),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.tertiary,
                foregroundColor: Theme.of(context).colorScheme.surface,
              ),
              child: const Text('Done'),
            ),
          ),
        ],
      ),
    );
  }

  static void show(
    BuildContext context, {
    required List<String> initialLinks,
    required Function(List<String>) onLinksChanged,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => LinkManagementBottomSheet(
        initialLinks: initialLinks,
        onLinksChanged: onLinksChanged,
      ),
    );
  }
}
