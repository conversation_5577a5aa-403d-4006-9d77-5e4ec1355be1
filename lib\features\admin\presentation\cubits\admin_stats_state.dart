/*
Admin Statistics States
*/

abstract class AdminStatsState {}

class AdminStatsInitial extends AdminStatsState {}

class AdminStatsLoading extends AdminStatsState {}

class AdminStatsLoaded extends AdminStatsState {
  final Map<String, dynamic> stats;
  final List<Map<String, dynamic>> topPosts;

  AdminStatsLoaded({required this.stats, required this.topPosts});
}

class AdminStatsError extends AdminStatsState {
  final String message;

  AdminStatsError(this.message);
}
