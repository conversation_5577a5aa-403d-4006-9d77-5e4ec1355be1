import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moneymouthy/common/components/loading_indicator.dart';
import 'package:moneymouthy/features/profile/presentation/components/user_tile.dart';
import 'package:moneymouthy/features/search/presentation/cubits/search_cubit.dart';
import 'package:moneymouthy/features/search/presentation/cubits/search_states.dart';
import 'package:moneymouthy/responsive/constrained_scaffold.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final searchController = TextEditingController();
  late final searchCubit = context.read<SearchCubit>();

  void onSearchChanged() {
    final query = searchController.text;
    searchCubit.searchUsers(query);
  }

  @override
  void initState() {
    super.initState();
    searchController.addListener(onSearchChanged);
    // Load all users initially
    searchCubit.searchUsers('');
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: TextField(
          controller: searchController,
          decoration: InputDecoration(
            hintText: 'Search users...',
            hintStyle: TextStyle(color: Theme.of(context).colorScheme.primary),
            border: InputBorder.none,
          ),
        ),
      ),
      body: BlocBuilder<SearchCubit, SearchStates>(
        builder: (context, state) {
          // loaded

          if (state is SearchLoaded) {
            // no users
            if (state.users.isEmpty) {
              return Center(child: Text('No users found...'));
            }

            // users
            return ListView.builder(
              itemCount: state.users.length,
              itemBuilder: (context, index) {
                final user = state.users[index];
                return UserTile(user: user);
              },
            );
          }
          // loading...
          else if (state is SearchLoading) {
            return Center(child: LoadingIndicator());
          }
          // error
          else if (state is SearchError) {
            return Center(child: Text(state.message));
          }
          // default
          return const Center(
            child: Text(
              'Search for users...',
              style: TextStyle(color: Colors.grey),
            ),
          );
        },
      ),
    );
  }
}
