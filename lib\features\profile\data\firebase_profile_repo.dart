import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:moneymouthy/features/post/domain/entities/post.dart';
import 'package:moneymouthy/features/profile/domain/entities/profile_user.dart';
import 'package:moneymouthy/features/profile/domain/repos/profile_repo.dart';

class FirebaseProfileRepo implements ProfileRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
  @override
  Future<ProfileUser?> fetchUserProfile(String uid) async {
    try {
      // get user document from firestore
      final userDoc = await firebaseFirestore
          .collection('users')
          .doc(uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data();
        if (userData != null) {
          // fetch following and followers from data
          final following = List<String>.from(userData['following'] ?? []);
          final followers = List<String>.from(userData['followers'] ?? []);

          return ProfileUser(
            uid: uid,
            name: userData['name'] ?? '',
            email: userData['email'],
            bio: userData['bio'] ?? '',
            profileImageUrl: userData['profileImageUrl'].toString(),
            following: following,
            followers: followers,
            selectedPostCategory: PostCategory.values.firstWhere(
              (e) => e.name == userData['selectedPostCategory'],
              orElse: () => PostCategory.politics,
            ),
            postAmount: (userData['postAmount'] as num?)?.toDouble() ?? 0.05,
          );
        }
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  @override
  Future<void> updateProfile(ProfileUser updatedProfile) async {
    try {
      await firebaseFirestore
          .collection('users')
          .doc(updatedProfile.uid)
          .update({
            'bio': updatedProfile.bio,
            'profileImageUrl': updatedProfile.profileImageUrl,
            'selectedPostCategory': updatedProfile.selectedPostCategory.name,
            'postAmount': updatedProfile.postAmount,
          });
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<void> toggleFollow(String currentUid, String targetUid) async {
    try {
      // get current user document from firestore
      final currentUserDoc = await firebaseFirestore
          .collection('users')
          .doc(currentUid)
          .get();
      // get target user document from firestore
      final targetUserDoc = await firebaseFirestore
          .collection('users')
          .doc(targetUid)
          .get();

      if (currentUserDoc.exists && targetUserDoc.exists) {
        final currentUser = currentUserDoc.data();
        final targetUser = targetUserDoc.data();

        if (currentUser != null && targetUser != null) {
          final List<String> currentFollowing =
              (currentUser['following'] as List<dynamic>?)
                  ?.map((e) => e.toString())
                  .toList() ??
              [];

          // check if target user is already followed by current user
          final isFollowing = currentFollowing.contains(targetUid);

          if (isFollowing) {
            //unfollow
            await firebaseFirestore.collection('users').doc(currentUid).update({
              'following': FieldValue.arrayRemove([targetUid]),
            });
            await firebaseFirestore.collection('users').doc(targetUid).update({
              'followers': FieldValue.arrayRemove([currentUid]),
            });
          } else {
            // follow
            await firebaseFirestore.collection('users').doc(currentUid).update({
              'following': FieldValue.arrayUnion([targetUid]),
            });
            await firebaseFirestore.collection('users').doc(targetUid).update({
              'followers': FieldValue.arrayUnion([currentUid]),
            });
          }
        }
      }
    } catch (e) {
      throw Exception("Failed to toggle follow: $e");
    }
  }

  @override
  Future<List<ProfileUser>> getUnfollowingUsers(String currentUid) async {
    try {
      // Get current user's following list
      final currentUserDoc = await firebaseFirestore
          .collection('users')
          .doc(currentUid)
          .get();

      if (!currentUserDoc.exists) {
        return [];
      }

      final currentUserData = currentUserDoc.data();
      final following = List<String>.from(currentUserData?['following'] ?? []);

      // Get all users
      final allUsersQuery = await firebaseFirestore.collection('users').get();

      // Filter out current user and users already being followed
      final unfollowingUsers = allUsersQuery.docs
          .where((doc) => doc.id != currentUid && !following.contains(doc.id))
          .map((doc) {
            final data = doc.data();
            data['uid'] = doc.id;
            return ProfileUser.fromJson(data);
          })
          .toList();

      return unfollowingUsers;
    } catch (e) {
      throw Exception("Failed to get unfollowing users: $e");
    }
  }

  /// Makes all users follow each other (mutual following)
  /// Uses batched writes for optimal performance
  @override
  Future<void> makeAllUsersMutuallyFollow() async {
    try {
      // Get all users
      final allUsersQuery = await firebaseFirestore.collection('users').get();
      final allUserIds = allUsersQuery.docs.map((doc) => doc.id).toList();

      if (allUserIds.length < 2) {
        return; // Need at least 2 users to follow each other
      }

      const int batchSize = 500; // Firestore batch limit
      List<WriteBatch> batches = [];
      WriteBatch currentBatch = firebaseFirestore.batch();
      int operationCount = 0;

      // For each user, make them follow all other users
      for (String userId in allUserIds) {
        final otherUsers = allUserIds.where((id) => id != userId).toList();

        final userRef = firebaseFirestore.collection('users').doc(userId);

        // Update following list (all other users)
        currentBatch.update(userRef, {'following': otherUsers});

        // Update followers list (all other users)
        currentBatch.update(userRef, {'followers': otherUsers});

        operationCount += 2; // Two operations per user

        // Check if we need to start a new batch
        if (operationCount >= batchSize) {
          batches.add(currentBatch);
          currentBatch = firebaseFirestore.batch();
          operationCount = 0;
        }
      }

      // Add the last batch if it has operations
      if (operationCount > 0) {
        batches.add(currentBatch);
      }

      // Commit all batches
      for (WriteBatch batch in batches) {
        await batch.commit();
      }
    } catch (e) {
      throw Exception("Failed to make all users mutually follow: $e");
    }
  }

  /// Makes a specific user follow all other users they're not already following
  /// Uses batched writes for optimal performance
  @override
  Future<void> makeUserFollowAllRemaining(String currentUid) async {
    try {
      // Get current user's following list
      final currentUserDoc = await firebaseFirestore
          .collection('users')
          .doc(currentUid)
          .get();

      if (!currentUserDoc.exists) {
        throw Exception("Current user not found");
      }

      final currentUserData = currentUserDoc.data();
      final currentFollowing = List<String>.from(
        currentUserData?['following'] ?? [],
      );

      // Get all users
      final allUsersQuery = await firebaseFirestore.collection('users').get();

      // Find users not currently being followed (excluding current user)
      final usersToFollow = allUsersQuery.docs
          .where(
            (doc) => doc.id != currentUid && !currentFollowing.contains(doc.id),
          )
          .map((doc) => doc.id)
          .toList();

      if (usersToFollow.isEmpty) {
        return; // Already following everyone
      }

      const int batchSize = 500; // Firestore batch limit
      List<WriteBatch> batches = [];
      WriteBatch currentBatch = firebaseFirestore.batch();
      int operationCount = 0;

      // Add current user to following list of all target users (batch operations)
      for (String targetUid in usersToFollow) {
        final targetUserRef = firebaseFirestore
            .collection('users')
            .doc(targetUid);

        currentBatch.update(targetUserRef, {
          'followers': FieldValue.arrayUnion([currentUid]),
        });

        operationCount++;

        // Check if we need to start a new batch
        if (operationCount >= batchSize) {
          batches.add(currentBatch);
          currentBatch = firebaseFirestore.batch();
          operationCount = 0;
        }
      }

      // Add the last batch if it has operations
      if (operationCount > 0) {
        batches.add(currentBatch);
      }

      // Update current user's following list (single operation)
      final updatedFollowing = [...currentFollowing, ...usersToFollow];
      final currentUserBatch = firebaseFirestore.batch();
      currentUserBatch.update(
        firebaseFirestore.collection('users').doc(currentUid),
        {'following': updatedFollowing},
      );
      batches.add(currentUserBatch);

      // Commit all batches
      for (WriteBatch batch in batches) {
        await batch.commit();
      }
    } catch (e) {
      throw Exception("Failed to make user follow all remaining: $e");
    }
  }

  /// Alternative optimized version using fewer read operations
  /// Makes a user follow all remaining users with minimal database reads
  @override
  Future<void> makeUserFollowAllRemainingOptimized(String currentUid) async {
    try {
      // Single query to get all users including current user
      final allUsersQuery = await firebaseFirestore.collection('users').get();

      ProfileUser? currentUser;
      List<String> allOtherUserIds = [];

      // Process all users in one pass
      for (var doc in allUsersQuery.docs) {
        if (doc.id == currentUid) {
          final data = doc.data();
          currentUser = ProfileUser(
            uid: currentUid,
            name: data['name'] ?? '',
            email: data['email'] ?? '',
            bio: data['bio'] ?? '',
            profileImageUrl: data['profileImageUrl']?.toString() ?? '',
            following: List<String>.from(data['following'] ?? []),
            followers: List<String>.from(data['followers'] ?? []),
            selectedPostCategory: PostCategory.values.firstWhere(
              (e) => e.name == data['selectedPostCategory'],
              orElse: () => PostCategory.politics,
            ),
            postAmount: (data['postAmount'] as num?)?.toDouble() ?? 0.05,
          );
        } else {
          allOtherUserIds.add(doc.id);
        }
      }

      if (currentUser == null) {
        throw Exception("Current user not found");
      }

      // Find users to follow
      final usersToFollow = allOtherUserIds
          .where((id) => !currentUser!.following.contains(id))
          .toList();

      if (usersToFollow.isEmpty) {
        return; // Already following everyone
      }

      // Create batched operations
      const int batchSize =
          400; // Conservative limit to account for array operations
      List<WriteBatch> batches = [];

      // Split users to follow into chunks for batch processing
      for (int i = 0; i < usersToFollow.length; i += batchSize) {
        final chunk = usersToFollow.skip(i).take(batchSize).toList();
        final batch = firebaseFirestore.batch();

        // Add current user to followers of each user in chunk
        for (String targetUid in chunk) {
          batch.update(firebaseFirestore.collection('users').doc(targetUid), {
            'followers': FieldValue.arrayUnion([currentUid]),
          });
        }

        batches.add(batch);
      }

      // Update current user's following list
      final finalBatch = firebaseFirestore.batch();
      final updatedFollowing = [...currentUser.following, ...usersToFollow];
      finalBatch.update(firebaseFirestore.collection('users').doc(currentUid), {
        'following': updatedFollowing,
      });
      batches.add(finalBatch);

      // Commit all batches
      for (WriteBatch batch in batches) {
        await batch.commit();
      }
    } catch (e) {
      throw Exception(
        "Failed to make user follow all remaining (optimized): $e",
      );
    }
  }
}
