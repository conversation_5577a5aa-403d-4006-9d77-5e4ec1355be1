import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:moneymouthy/features/poll/domain/entities/poll.dart';
import 'package:moneymouthy/features/poll/domain/repos/poll_repo.dart';

class FirebasePollRepo implements PollRepo {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _pollsCollection = 'polls';

  @override
  Future<void> createPoll(Poll poll) async {
    try {
      await _firestore
          .collection(_pollsCollection)
          .doc(poll.id)
          .set(poll.toJson());
    } catch (e) {
      throw Exception('Failed to create poll: $e');
    }
  }

  @override
  Future<Poll?> getPollById(String pollId) async {
    try {
      final doc = await _firestore
          .collection(_pollsCollection)
          .doc(pollId)
          .get();

      if (doc.exists && doc.data() != null) {
        return Poll.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get poll: $e');
    }
  }

  @override
  Future<Poll?> getPollByPostId(String postId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_pollsCollection)
          .where('postId', isEqualTo: postId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return Poll.fromJson(querySnapshot.docs.first.data());
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get poll by post ID: $e');
    }
  }

  @override
  Future<void> voteOnPoll(
    String pollId,
    String userId,
    PollOption option,
  ) async {
    try {
      final pollRef = _firestore.collection(_pollsCollection).doc(pollId);

      await _firestore.runTransaction((transaction) async {
        final pollDoc = await transaction.get(pollRef);

        if (!pollDoc.exists) {
          throw Exception('Poll not found');
        }

        final poll = Poll.fromJson(pollDoc.data()!);

        // Check if poll is expired
        if (poll.isExpired) {
          throw Exception('Poll has expired');
        }

        // Remove existing vote if user has already voted (for single vote polls)
        final updatedVotes = poll.votes
            .where((vote) => vote.userId != userId)
            .toList();

        // Add new vote
        final newVote = PollVote(
          userId: userId,
          option: option,
          timestamp: DateTime.now(),
        );
        updatedVotes.add(newVote);

        final updatedPoll = poll.copyWith(votes: updatedVotes);
        transaction.update(pollRef, updatedPoll.toJson());
      });
    } catch (e) {
      throw Exception('Failed to vote on poll: $e');
    }
  }

  @override
  Future<void> removeVoteFromPoll(String pollId, String userId) async {
    try {
      final pollRef = _firestore.collection(_pollsCollection).doc(pollId);

      await _firestore.runTransaction((transaction) async {
        final pollDoc = await transaction.get(pollRef);

        if (!pollDoc.exists) {
          throw Exception('Poll not found');
        }

        final poll = Poll.fromJson(pollDoc.data()!);

        // Remove user's vote
        final updatedVotes = poll.votes
            .where((vote) => vote.userId != userId)
            .toList();

        final updatedPoll = poll.copyWith(votes: updatedVotes);
        transaction.update(pollRef, updatedPoll.toJson());
      });
    } catch (e) {
      throw Exception('Failed to remove vote from poll: $e');
    }
  }

  @override
  Future<void> updatePoll(Poll poll) async {
    try {
      await _firestore
          .collection(_pollsCollection)
          .doc(poll.id)
          .update(poll.toJson());
    } catch (e) {
      throw Exception('Failed to update poll: $e');
    }
  }

  @override
  Future<void> deletePoll(String pollId) async {
    try {
      await _firestore.collection(_pollsCollection).doc(pollId).delete();
    } catch (e) {
      throw Exception('Failed to delete poll: $e');
    }
  }

  @override
  Future<List<Poll>> getPollsByCreator(String creatorId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_pollsCollection)
          .where('creatorId', isEqualTo: creatorId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Poll.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to get polls by creator: $e');
    }
  }

  @override
  Future<List<Poll>> getPollsVotedByUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_pollsCollection)
          .where(
            'votes',
            arrayContainsAny: [
              {'userId': userId},
            ],
          )
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Poll.fromJson(doc.data()))
          .where((poll) => poll.hasUserVoted(userId))
          .toList();
    } catch (e) {
      throw Exception('Failed to get polls voted by user: $e');
    }
  }

  @override
  Stream<Poll?> streamPoll(String pollId) {
    try {
      return _firestore
          .collection(_pollsCollection)
          .doc(pollId)
          .snapshots()
          .map((doc) {
            if (doc.exists && doc.data() != null) {
              return Poll.fromJson(doc.data()!);
            }
            return null;
          });
    } catch (e) {
      throw Exception('Failed to stream poll: $e');
    }
  }
}
